/**
 * DevTycoon Pro - UI Manager
 * Manages user interface updates and interactions
 */

class UIManager {
    constructor() {
        this.eventSystem = null;
        this.modalManager = null;
        this.notificationSystem = null;
        
        // UI state
        this.activeModals = [];
        this.notifications = [];
        
        // Update frequencies
        this.fastUpdateInterval = 100; // 10 FPS for critical updates
        this.slowUpdateInterval = 1000; // 1 FPS for non-critical updates
        
        this.lastFastUpdate = 0;
        this.lastSlowUpdate = 0;
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.modalManager = new ModalManager();
        this.notificationSystem = new NotificationSystem();
        
        this.setupEventListeners();
        this.initializeComponents();
        
        console.log('🖥️ UI Manager initialized');
    }
    
    setupEventListeners() {
        // Listen for UI update events
        this.eventSystem.on('uiUpdate', (data) => {
            this.handleUIUpdate(data);
        });
        
        // Listen for notification events
        this.eventSystem.on('notification', (notification) => {
            this.notificationSystem.show(notification);
        });
        
        // Listen for modal events
        this.eventSystem.on('showModal', (modalType, data) => {
            this.showModal(modalType, data);
        });
        
        this.eventSystem.on('hideModal', (modalId) => {
            this.hideModal(modalId);
        });
    }
    
    initializeComponents() {
        this.modalManager.init(this.eventSystem);
        this.notificationSystem.init(this.eventSystem);
        
        // Setup global UI event handlers
        this.setupGlobalHandlers();
    }
    
    setupGlobalHandlers() {
        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModals.length > 0) {
                this.hideTopModal();
            }
        });
        
        // Click outside modal to close
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-container')) {
                this.hideTopModal();
            }
        });
    }
    
    update(deltaTime) {
        const now = Date.now();
        
        // Fast updates (animations, real-time data)
        if (now - this.lastFastUpdate >= this.fastUpdateInterval) {
            this.updateFast();
            this.lastFastUpdate = now;
        }
        
        // Slow updates (static data, less critical info)
        if (now - this.lastSlowUpdate >= this.slowUpdateInterval) {
            this.updateSlow();
            this.lastSlowUpdate = now;
        }
        
        // Update components
        this.notificationSystem.update(deltaTime);
    }
    
    updateFast() {
        // Update animations and real-time elements
        this.updateProgressBars();
        this.updateCounters();
    }
    
    updateSlow() {
        // Update less critical UI elements
        this.updateStaticDisplays();
    }
    
    handleUIUpdate(data) {
        switch (data.type) {
            case 'company':
                this.updateCompanyDisplay(data.company);
                break;
            case 'employees':
                this.updateEmployeesDisplay(data.employees, data.stats);
                break;
            case 'projects':
                this.updateProjectsDisplay(data.projects);
                break;
            case 'date':
                this.updateDateDisplay(data);
                break;
        }
    }
    
    updateCompanyDisplay(company) {
        // Update cash with animation
        this.animateNumberChange('cashDisplay', company.cash, (num) => `$${this.formatNumber(num)}`);
        
        // Update reputation
        this.animateNumberChange('reputationDisplay', company.reputation, (num) => `★ ${num}`);
        
        // Update company name
        const companyNameDisplay = document.getElementById('companyNameDisplay');
        if (companyNameDisplay) {
            companyNameDisplay.textContent = company.name;
        }
    }
    
    updateEmployeesDisplay(employees, stats) {
        // Update employee count
        const employeeCountDisplay = document.getElementById('employeeCountDisplay');
        if (employeeCountDisplay) {
            employeeCountDisplay.textContent = `👥 ${employees.length}`;
        }
        
        // Update employees list in sidebar
        this.updateEmployeesList(employees);
    }
    
    updateEmployeesList(employees) {
        const employeesList = document.getElementById('employeesList');
        if (!employeesList) return;
        
        if (employees.length === 0) {
            employeesList.innerHTML = '<p class="no-employees">No employees hired</p>';
            return;
        }
        
        const employeesHTML = employees.map(emp => `
            <div class="employee-item" data-employee-id="${emp.id}">
                <div class="employee-header">
                    <span class="employee-name">${emp.name}</span>
                    <span class="employee-status ${emp.isWorking ? 'working' : 'idle'}">
                        ${emp.isWorking ? '🔧' : '💤'}
                    </span>
                </div>
                <div class="employee-details">
                    <div class="employee-role">${emp.roleName}</div>
                    <div class="employee-happiness">😊 ${emp.happiness}%</div>
                </div>
            </div>
        `).join('');
        
        employeesList.innerHTML = employeesHTML;
    }
    
    updateProjectsDisplay(projects) {
        const activeProjectsList = document.getElementById('activeProjectsList');
        if (!activeProjectsList) return;
        
        if (projects.length === 0) {
            activeProjectsList.innerHTML = '<p class="no-projects">No active projects</p>';
            return;
        }
        
        const projectsHTML = projects.map(project => `
            <div class="project-item" data-project-id="${project.id}">
                <div class="project-name">${project.name}</div>
                <div class="project-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${project.progress}%"></div>
                    </div>
                    <span class="progress-text">${Math.round(project.progress)}%</span>
                </div>
                <div class="project-team">👥 ${project.assignedEmployees.length}</div>
            </div>
        `).join('');
        
        activeProjectsList.innerHTML = projectsHTML;
    }
    
    updateDateDisplay(data) {
        const dateDisplay = document.getElementById('dateDisplay');
        if (dateDisplay) {
            dateDisplay.textContent = data.formattedDate;
            
            // Add visual indicator for working/non-working days
            dateDisplay.className = data.isWorkingDay ? 'working-day' : 'non-working-day';
        }
    }
    
    updateProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar-animated .progress-fill');
        progressBars.forEach(bar => {
            // Add shimmer effect for active progress bars
            if (!bar.classList.contains('shimmer')) {
                bar.classList.add('shimmer');
            }
        });
    }
    
    updateCounters() {
        // Update any animated counters
        const counters = document.querySelectorAll('[data-counter]');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-counter'));
            const current = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
            
            if (current !== target) {
                const diff = target - current;
                const step = Math.ceil(Math.abs(diff) / 10);
                const newValue = current + (diff > 0 ? step : -step);
                
                counter.textContent = counter.textContent.replace(/[0-9,]+/, this.formatNumber(newValue));
                
                if (Math.abs(target - newValue) <= step) {
                    counter.textContent = counter.textContent.replace(/[0-9,]+/, this.formatNumber(target));
                }
            }
        });
    }
    
    updateStaticDisplays() {
        // Update elements that don't need frequent updates
        this.updateMarketTrends();
        this.updateGameStatus();
    }
    
    updateMarketTrends() {
        const marketTrends = document.getElementById('marketTrends');
        if (marketTrends) {
            // This would be populated by the market system
            // For now, show placeholder data
            const trends = [
                { name: 'Web Development', value: '📈 High', trend: 'up' },
                { name: 'Mobile Apps', value: '📊 Medium', trend: 'stable' },
                { name: 'AI/ML', value: '🚀 Very High', trend: 'up' },
                { name: 'Games', value: '📉 Low', trend: 'down' }
            ];
            
            const trendsHTML = trends.map(trend => `
                <div class="trend-item trend-${trend.trend}">
                    <span class="trend-name">${trend.name}</span>
                    <span class="trend-value">${trend.value}</span>
                </div>
            `).join('');
            
            marketTrends.innerHTML = trendsHTML;
        }
    }
    
    updateGameStatus() {
        const gameStatus = document.getElementById('gameStatus');
        if (gameStatus) {
            // Update based on current game state
            gameStatus.textContent = 'Running smoothly';
        }
    }
    
    // Modal Management
    showModal(modalType, data = {}) {
        return this.modalManager.show(modalType, data);
    }
    
    hideModal(modalId) {
        return this.modalManager.hide(modalId);
    }
    
    hideTopModal() {
        if (this.activeModals.length > 0) {
            const topModal = this.activeModals[this.activeModals.length - 1];
            this.hideModal(topModal.id);
        }
    }
    
    // Utility Methods
    animateNumberChange(elementId, targetValue, formatter = null) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const currentText = element.textContent;
        const currentValue = parseInt(currentText.replace(/[^0-9]/g, '')) || 0;
        
        if (currentValue === targetValue) return;
        
        // Set up animation
        element.setAttribute('data-counter', targetValue);
        
        if (formatter) {
            element.textContent = formatter(targetValue);
        } else {
            element.textContent = targetValue.toString();
        }
        
        // Add visual feedback for changes
        if (targetValue > currentValue) {
            element.classList.add('value-increase');
            setTimeout(() => element.classList.remove('value-increase'), 1000);
        } else if (targetValue < currentValue) {
            element.classList.add('value-decrease');
            setTimeout(() => element.classList.remove('value-decrease'), 1000);
        }
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    }
    
    showTooltip(element, content, position = 'top') {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = `tooltip tooltip-${position}`;
        tooltip.innerHTML = content;
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
        let top = rect.top - tooltipRect.height - 10;
        
        if (position === 'bottom') {
            top = rect.bottom + 10;
        }
        
        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
        
        // Show tooltip
        tooltip.classList.add('show');
        
        // Auto-hide after delay
        setTimeout(() => {
            tooltip.classList.remove('show');
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 3000);
    }
    
    render() {
        // Render any canvas-based UI elements
        this.renderOverlays();
    }
    
    renderOverlays() {
        // Render UI overlays on the game canvas
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            // Update overlay content if needed
        }
    }
}
