/**
 * DevTycoon Pro - Market System
 * Simulates market conditions and trends
 */

class MarketSystem {
    constructor() {
        this.eventSystem = null;
        this.trends = new Map();
        this.competitors = [];
        this.marketData = {
            totalMarketSize: 1000000000, // $1B market
            growthRate: 0.05, // 5% annual growth
            volatility: 0.1 // 10% volatility
        };
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.initializeMarketTrends();
        this.setupEventListeners();
        console.log('📊 Market System initialized');
    }
    
    setupEventListeners() {
        this.eventSystem.on('newMonth', () => {
            this.updateMarketTrends();
        });
    }
    
    initializeMarketTrends() {
        // Initialize market trends for different technologies
        const initialTrends = [
            { name: 'Web Development', demand: 80, growth: 0.02 },
            { name: 'Mobile Apps', demand: 90, growth: 0.05 },
            { name: 'AI/ML', demand: 95, growth: 0.08 },
            { name: 'Games', demand: 60, growth: -0.01 },
            { name: 'Enterprise Software', demand: 75, growth: 0.03 },
            { name: 'E-commerce', demand: 85, growth: 0.04 }
        ];
        
        initialTrends.forEach(trend => {
            this.trends.set(trend.name, {
                ...trend,
                history: [trend.demand],
                lastUpdate: Date.now()
            });
        });
    }
    
    updateMarketTrends() {
        this.trends.forEach((trend, name) => {
            // Apply growth and random fluctuation
            const randomFactor = (Math.random() - 0.5) * 0.1; // ±5% random change
            const newDemand = Math.max(0, Math.min(100, 
                trend.demand * (1 + trend.growth + randomFactor)
            ));
            
            trend.demand = newDemand;
            trend.history.push(newDemand);
            
            // Keep only last 12 months of history
            if (trend.history.length > 12) {
                trend.history.shift();
            }
            
            trend.lastUpdate = Date.now();
        });
        
        this.eventSystem.emit('marketTrendsUpdated', this.getMarketSnapshot());
    }
    
    getMarketSnapshot() {
        const snapshot = {};
        this.trends.forEach((trend, name) => {
            snapshot[name] = {
                demand: Math.round(trend.demand),
                growth: trend.growth,
                trend: this.getTrendDirection(trend),
                history: [...trend.history]
            };
        });
        return snapshot;
    }
    
    getTrendDirection(trend) {
        if (trend.history.length < 2) return 'stable';
        
        const recent = trend.history.slice(-3);
        const avg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
        const current = trend.demand;
        
        if (current > avg * 1.05) return 'up';
        if (current < avg * 0.95) return 'down';
        return 'stable';
    }
    
    getDemandMultiplier(projectType) {
        // Map project types to market trends
        const typeMapping = {
            website: 'Web Development',
            webapp: 'Web Development',
            mobile: 'Mobile Apps',
            game: 'Games',
            enterprise: 'Enterprise Software',
            ecommerce: 'E-commerce'
        };
        
        const trendName = typeMapping[projectType] || 'Web Development';
        const trend = this.trends.get(trendName);
        
        if (!trend) return 1.0;
        
        // Convert demand (0-100) to multiplier (0.5-2.0)
        return 0.5 + (trend.demand / 100) * 1.5;
    }
    
    // Placeholder methods for future features
    generateContracts() {
        // Generate available contracts based on market conditions
        return [];
    }
    
    updateCompetitors() {
        // Update competitor activities
    }
    
    getSaveData() {
        return {
            trends: Object.fromEntries(this.trends),
            marketData: this.marketData
        };
    }
    
    loadSaveData(data) {
        if (data.trends) {
            this.trends = new Map(Object.entries(data.trends));
        }
        if (data.marketData) {
            this.marketData = data.marketData;
        }
    }
}
