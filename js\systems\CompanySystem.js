/**
 * DevTycoon Pro - Company System
 * Manages company data, finances, reputation, and growth
 */

class CompanySystem {
    constructor() {
        this.company = null;
        this.eventSystem = null;
        this.timeManager = null;
        
        // Financial tracking
        this.monthlyExpenses = [];
        this.monthlyIncome = [];
        this.financialHistory = [];
        
        // Reputation factors
        this.reputationFactors = {
            projectQuality: 0.3,
            customerSatisfaction: 0.25,
            employeeHappiness: 0.2,
            marketPresence: 0.15,
            innovation: 0.1
        };
        
        // Company growth stages
        this.growthStages = [
            { name: 'Startup', minEmployees: 1, maxEmployees: 5, benefits: { creativityBonus: 0.2 } },
            { name: 'Small Business', minEmployees: 6, maxEmployees: 20, benefits: { efficiencyBonus: 0.1 } },
            { name: 'Medium Company', minEmployees: 21, maxEmployees: 100, benefits: { marketingBonus: 0.15 } },
            { name: 'Large Corporation', minEmployees: 101, maxEmployees: 500, benefits: { resourceBonus: 0.2 } },
            { name: 'Enterprise', minEmployees: 501, maxEmployees: Infinity, benefits: { globalReach: 0.3 } }
        ];
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.timeManager = gameEngine.timeManager;
        
        this.setupEventListeners();
        console.log('🏢 Company System initialized');
    }
    
    setupEventListeners() {
        // Listen for time events
        this.eventSystem.on('newMonth', () => {
            this.processMonthlyFinancials();
        });
        
        this.eventSystem.on('newYear', () => {
            this.processYearlyReview();
        });
        
        // Listen for project completion
        this.eventSystem.on('projectCompleted', (project) => {
            this.onProjectCompleted(project);
        });
        
        // Listen for employee changes
        this.eventSystem.on('employeeHired', () => {
            this.updateCompanyStage();
        });
        
        this.eventSystem.on('employeeFired', () => {
            this.updateCompanyStage();
        });
    }
    
    createCompany(companyData) {
        this.company = {
            // Basic info
            name: companyData.name,
            founder: companyData.founder,
            foundedDate: new Date(companyData.startYear, 0, 1),
            
            // Financial data
            cash: companyData.startingCapital,
            totalRevenue: 0,
            totalExpenses: 0,
            netWorth: companyData.startingCapital,
            
            // Reputation and metrics
            reputation: 50, // 0-100 scale
            marketShare: 0,
            customerSatisfaction: 50,
            employeeHappiness: 75,
            
            // Company stats
            employeeCount: 0,
            projectsCompleted: 0,
            totalSales: 0,
            
            // Growth and achievements
            stage: 'Startup',
            achievements: [],
            milestones: [],
            
            // Settings
            difficulty: companyData.difficulty,
            
            // Advanced metrics
            productivity: 1.0,
            innovation: 0.5,
            marketPresence: 0.1,
            brandValue: 0
        };
        
        this.updateUI();
        this.eventSystem.emit('companyCreated', this.company);
        
        console.log(`🏢 Company "${this.company.name}" created!`);
        return this.company;
    }
    
    // Financial Management
    addIncome(amount, source = 'unknown') {
        if (amount <= 0) return false;
        
        this.company.cash += amount;
        this.company.totalRevenue += amount;
        this.company.netWorth += amount;
        
        this.monthlyIncome.push({
            amount,
            source,
            date: new Date(this.timeManager.currentDate)
        });
        
        this.eventSystem.emit('incomeAdded', { amount, source, newBalance: this.company.cash });
        this.updateUI();
        
        return true;
    }
    
    addExpense(amount, category = 'unknown') {
        if (amount <= 0) return false;
        
        this.company.cash -= amount;
        this.company.totalExpenses += amount;
        this.company.netWorth -= amount;
        
        this.monthlyExpenses.push({
            amount,
            category,
            date: new Date(this.timeManager.currentDate)
        });
        
        this.eventSystem.emit('expenseAdded', { amount, category, newBalance: this.company.cash });
        this.updateUI();
        
        return true;
    }
    
    canAfford(amount) {
        return this.company.cash >= amount;
    }
    
    // Reputation Management
    adjustReputation(change, reason = 'unknown') {
        const oldReputation = this.company.reputation;
        this.company.reputation = Math.max(0, Math.min(100, this.company.reputation + change));
        
        if (this.company.reputation !== oldReputation) {
            this.eventSystem.emit('reputationChanged', {
                oldValue: oldReputation,
                newValue: this.company.reputation,
                change,
                reason
            });
            
            this.updateUI();
        }
    }
    
    calculateReputation() {
        let totalReputation = 0;
        
        // Project quality impact
        const avgProjectQuality = this.getAverageProjectQuality();
        totalReputation += avgProjectQuality * this.reputationFactors.projectQuality;
        
        // Customer satisfaction
        totalReputation += this.company.customerSatisfaction * this.reputationFactors.customerSatisfaction;
        
        // Employee happiness
        totalReputation += this.company.employeeHappiness * this.reputationFactors.employeeHappiness;
        
        // Market presence
        totalReputation += this.company.marketPresence * 100 * this.reputationFactors.marketPresence;
        
        // Innovation
        totalReputation += this.company.innovation * 100 * this.reputationFactors.innovation;
        
        return Math.round(totalReputation);
    }
    
    // Company Growth
    updateCompanyStage() {
        const employeeCount = this.company.employeeCount;
        
        for (const stage of this.growthStages) {
            if (employeeCount >= stage.minEmployees && employeeCount <= stage.maxEmployees) {
                if (this.company.stage !== stage.name) {
                    const oldStage = this.company.stage;
                    this.company.stage = stage.name;
                    
                    this.eventSystem.emit('companyStageChanged', {
                        oldStage,
                        newStage: stage.name,
                        benefits: stage.benefits
                    });
                    
                    this.addAchievement(`Reached ${stage.name} stage`);
                }
                break;
            }
        }
    }
    
    getCurrentStageInfo() {
        return this.growthStages.find(stage => stage.name === this.company.stage);
    }
    
    // Achievements and Milestones
    addAchievement(name, description = '', reward = null) {
        const achievement = {
            name,
            description,
            reward,
            unlockedDate: new Date(this.timeManager.currentDate),
            id: `achievement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        
        this.company.achievements.push(achievement);
        
        this.eventSystem.emit('achievementUnlocked', achievement);
        
        if (reward) {
            this.applyAchievementReward(reward);
        }
    }
    
    addMilestone(name, value, target) {
        const milestone = {
            name,
            value,
            target,
            progress: value / target,
            completed: value >= target,
            date: new Date(this.timeManager.currentDate)
        };
        
        this.company.milestones.push(milestone);
        
        if (milestone.completed) {
            this.eventSystem.emit('milestoneCompleted', milestone);
        }
    }
    
    applyAchievementReward(reward) {
        switch (reward.type) {
            case 'cash':
                this.addIncome(reward.amount, 'Achievement Reward');
                break;
            case 'reputation':
                this.adjustReputation(reward.amount, 'Achievement Reward');
                break;
            case 'unlock':
                this.eventSystem.emit('featureUnlocked', reward.feature);
                break;
        }
    }
    
    // Monthly and Yearly Processing
    processMonthlyFinancials() {
        const monthlyIncome = this.monthlyIncome.reduce((sum, income) => sum + income.amount, 0);
        const monthlyExpenses = this.monthlyExpenses.reduce((sum, expense) => sum + expense.amount, 0);
        const netProfit = monthlyIncome - monthlyExpenses;
        
        const financialReport = {
            month: this.timeManager.currentDate.getMonth(),
            year: this.timeManager.currentDate.getFullYear(),
            income: monthlyIncome,
            expenses: monthlyExpenses,
            netProfit,
            cash: this.company.cash,
            employeeCount: this.company.employeeCount
        };
        
        this.financialHistory.push(financialReport);
        
        // Clear monthly tracking
        this.monthlyIncome = [];
        this.monthlyExpenses = [];
        
        this.eventSystem.emit('monthlyReport', financialReport);
        
        // Check for financial milestones
        this.checkFinancialMilestones(financialReport);
    }
    
    processYearlyReview() {
        const yearlyData = this.getYearlyFinancials();
        
        this.eventSystem.emit('yearlyReport', yearlyData);
        
        // Update company metrics
        this.updateAnnualMetrics();
    }
    
    checkFinancialMilestones(report) {
        // Check for profit milestones
        if (report.netProfit > 0 && !this.hasAchievement('First Profit')) {
            this.addAchievement('First Profit', 'Made your first monthly profit!', 
                { type: 'reputation', amount: 5 });
        }
        
        if (report.netProfit >= 10000 && !this.hasAchievement('Big Profit')) {
            this.addAchievement('Big Profit', 'Made $10,000 profit in a month!', 
                { type: 'cash', amount: 5000 });
        }
    }
    
    // Utility Methods
    hasAchievement(name) {
        return this.company.achievements.some(achievement => achievement.name === name);
    }
    
    getAverageProjectQuality() {
        // This would be calculated from completed projects
        // For now, return a placeholder
        return 75;
    }
    
    getYearlyFinancials() {
        const currentYear = this.timeManager.currentDate.getFullYear();
        const yearlyReports = this.financialHistory.filter(report => report.year === currentYear);
        
        return {
            year: currentYear,
            totalIncome: yearlyReports.reduce((sum, report) => sum + report.income, 0),
            totalExpenses: yearlyReports.reduce((sum, report) => sum + report.expenses, 0),
            totalProfit: yearlyReports.reduce((sum, report) => sum + report.netProfit, 0),
            averageEmployees: yearlyReports.reduce((sum, report) => sum + report.employeeCount, 0) / Math.max(1, yearlyReports.length),
            monthlyReports: yearlyReports
        };
    }
    
    updateAnnualMetrics() {
        // Update innovation based on R&D spending
        // Update market presence based on marketing
        // These would be calculated from actual game data
    }
    
    updateUI() {
        // Update cash display
        const cashDisplay = document.getElementById('cashDisplay');
        if (cashDisplay && this.company) {
            cashDisplay.textContent = `$${this.formatNumber(this.company.cash)}`;
        }
        
        // Update company name display
        const companyNameDisplay = document.getElementById('companyNameDisplay');
        if (companyNameDisplay && this.company) {
            companyNameDisplay.textContent = this.company.name;
        }
        
        // Update reputation display
        const reputationDisplay = document.getElementById('reputationDisplay');
        if (reputationDisplay && this.company) {
            reputationDisplay.textContent = `★ ${this.company.reputation}`;
        }
        
        // Emit UI update event
        this.eventSystem.emit('uiUpdate', {
            type: 'company',
            company: this.company
        });
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    }
    
    // Save/Load
    getSaveData() {
        return {
            company: this.company,
            monthlyExpenses: this.monthlyExpenses,
            monthlyIncome: this.monthlyIncome,
            financialHistory: this.financialHistory
        };
    }
    
    loadSaveData(data) {
        this.company = data.company;
        this.monthlyExpenses = data.monthlyExpenses || [];
        this.monthlyIncome = data.monthlyIncome || [];
        this.financialHistory = data.financialHistory || [];
        
        this.updateUI();
    }
    
    // Event handlers
    onProjectCompleted(project) {
        this.company.projectsCompleted++;
        this.addIncome(project.revenue, 'Project Revenue');
        this.adjustReputation(project.qualityScore / 10, 'Project Completion');
        
        this.updateUI();
    }
}
