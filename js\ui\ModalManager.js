/**
 * DevTycoon Pro - Modal Manager
 * Handles modal dialogs and popups
 */

class ModalManager {
    constructor() {
        this.eventSystem = null;
        this.activeModals = [];
        this.modalContainer = null;
        this.nextModalId = 1;
        
        // Modal templates
        this.modalTemplates = {
            hireEmployee: this.createHireEmployeeModal.bind(this),
            newProject: this.createNewProjectModal.bind(this),
            research: this.createResearchModal.bind(this),
            marketing: this.createMarketingModal.bind(this),
            office: this.createOfficeModal.bind(this),
            settings: this.createSettingsModal.bind(this),
            confirmation: this.createConfirmationModal.bind(this)
        };
    }
    
    init(eventSystem) {
        this.eventSystem = eventSystem;
        this.modalContainer = document.getElementById('modalContainer');
        
        if (!this.modalContainer) {
            console.error('Modal container not found!');
            return;
        }
        
        console.log('📋 Modal Manager initialized');
    }
    
    show(modalType, data = {}) {
        if (!this.modalTemplates[modalType]) {
            console.error(`Unknown modal type: ${modalType}`);
            return null;
        }
        
        const modalId = `modal_${this.nextModalId++}`;
        const modalContent = this.modalTemplates[modalType](data, modalId);
        
        const modal = {
            id: modalId,
            type: modalType,
            element: this.createModalWrapper(modalContent, modalId),
            data
        };
        
        this.activeModals.push(modal);
        this.modalContainer.appendChild(modal.element);
        this.modalContainer.classList.add('active');
        
        // Animate in
        setTimeout(() => {
            modal.element.classList.add('animate-scale-in');
        }, 10);
        
        // Setup modal-specific event handlers
        this.setupModalHandlers(modal);
        
        this.eventSystem.emit('modalShown', { modalId, modalType });
        
        return modalId;
    }
    
    hide(modalId) {
        const modalIndex = this.activeModals.findIndex(m => m.id === modalId);
        if (modalIndex === -1) return false;
        
        const modal = this.activeModals[modalIndex];
        
        // Animate out
        modal.element.classList.add('animate-scale-out');
        
        setTimeout(() => {
            if (modal.element.parentNode) {
                modal.element.parentNode.removeChild(modal.element);
            }
            
            this.activeModals.splice(modalIndex, 1);
            
            if (this.activeModals.length === 0) {
                this.modalContainer.classList.remove('active');
            }
            
            this.eventSystem.emit('modalHidden', { modalId, modalType: modal.type });
        }, 200);
        
        return true;
    }
    
    hideAll() {
        const modalIds = this.activeModals.map(m => m.id);
        modalIds.forEach(id => this.hide(id));
    }
    
    createModalWrapper(content, modalId) {
        const wrapper = document.createElement('div');
        wrapper.className = 'modal';
        wrapper.setAttribute('data-modal-id', modalId);
        wrapper.innerHTML = content;
        
        return wrapper;
    }
    
    setupModalHandlers(modal) {
        const element = modal.element;
        
        // Close button
        const closeBtn = element.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hide(modal.id);
            });
        }
        
        // Cancel buttons
        const cancelBtns = element.querySelectorAll('.btn-cancel');
        cancelBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.hide(modal.id);
            });
        });
        
        // Setup specific handlers based on modal type
        switch (modal.type) {
            case 'hireEmployee':
                this.setupHireEmployeeHandlers(modal);
                break;
            case 'newProject':
                this.setupNewProjectHandlers(modal);
                break;
            case 'research':
                this.setupResearchHandlers(modal);
                break;
        }
    }
    
    createHireEmployeeModal(data, modalId) {
        // Generate random employees to choose from
        const employeeSystem = window.gameEngine?.getSystem('employee');
        if (!employeeSystem) return '<div>Employee system not available</div>';
        
        const candidates = [];
        for (let i = 0; i < 6; i++) {
            candidates.push(employeeSystem.generateRandomEmployee());
        }
        
        const candidatesHTML = candidates.map(emp => `
            <div class="employee-card" data-employee-id="${emp.id}">
                <div class="employee-name">${emp.name}</div>
                <div class="employee-role">${emp.roleName}</div>
                <div class="employee-skills">
                    ${Object.entries(emp.skills).map(([skill, level]) => `
                        <div class="skill-item">
                            <span class="skill-name">${skill}</span>
                            <div class="skill-bar">
                                <div class="skill-fill" style="width: ${level}%"></div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="employee-salary">$${emp.salary}/month</div>
            </div>
        `).join('');
        
        return `
            <div class="modal-header">
                <h2 class="modal-title">Hire Employee</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Choose an employee to hire for your company:</p>
                <div class="employee-grid">
                    ${candidatesHTML}
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Cancel</button>
                <button class="menu-btn primary btn-hire" disabled>Hire Selected</button>
            </div>
        `;
    }
    
    createNewProjectModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">New Project</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="project-form">
                    <div class="form-field">
                        <label>Project Name</label>
                        <input type="text" id="projectName" placeholder="Enter project name">
                    </div>
                    <div class="form-field">
                        <label>Project Type</label>
                        <div class="project-type-grid">
                            <div class="project-type-card" data-type="website">
                                <div class="project-type-icon">🌐</div>
                                <div class="project-type-name">Website</div>
                                <div class="project-type-desc">Simple website project</div>
                            </div>
                            <div class="project-type-card" data-type="webapp">
                                <div class="project-type-icon">💻</div>
                                <div class="project-type-name">Web App</div>
                                <div class="project-type-desc">Complex web application</div>
                            </div>
                            <div class="project-type-card" data-type="mobile">
                                <div class="project-type-icon">📱</div>
                                <div class="project-type-name">Mobile App</div>
                                <div class="project-type-desc">Mobile application</div>
                            </div>
                            <div class="project-type-card" data-type="game">
                                <div class="project-type-icon">🎮</div>
                                <div class="project-type-name">Game</div>
                                <div class="project-type-desc">Video game project</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-field">
                            <label>Budget</label>
                            <input type="number" id="projectBudget" placeholder="Project budget">
                        </div>
                        <div class="form-field">
                            <label>Deadline (days)</label>
                            <input type="number" id="projectDeadline" placeholder="Days to complete">
                        </div>
                    </div>
                    <div class="form-field">
                        <label>Description</label>
                        <textarea id="projectDescription" placeholder="Project description"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Cancel</button>
                <button class="menu-btn primary btn-create-project">Create Project</button>
            </div>
        `;
    }
    
    createResearchModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">Research & Development</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Invest in research to unlock new technologies and capabilities:</p>
                <div class="research-tree">
                    <div class="research-node available">
                        <div class="research-title">Advanced Programming</div>
                        <div class="research-desc">Unlock advanced programming techniques</div>
                        <div class="research-cost">$5,000</div>
                    </div>
                    <div class="research-node available">
                        <div class="research-title">UI/UX Design</div>
                        <div class="research-desc">Improve user interface design capabilities</div>
                        <div class="research-cost">$3,000</div>
                    </div>
                    <div class="research-node locked">
                        <div class="research-title">AI Integration</div>
                        <div class="research-desc">Integrate AI into your projects</div>
                        <div class="research-cost">$15,000</div>
                    </div>
                    <div class="research-node available">
                        <div class="research-title">Mobile Development</div>
                        <div class="research-desc">Develop mobile applications</div>
                        <div class="research-cost">$7,500</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Close</button>
            </div>
        `;
    }
    
    createMarketingModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">Marketing Campaigns</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Launch marketing campaigns to increase your company's visibility:</p>
                <div class="marketing-options">
                    <div class="marketing-card">
                        <h3>Social Media Campaign</h3>
                        <p>Boost your online presence</p>
                        <div class="cost">$2,000</div>
                        <button class="menu-btn primary">Launch Campaign</button>
                    </div>
                    <div class="marketing-card">
                        <h3>Industry Conference</h3>
                        <p>Network with potential clients</p>
                        <div class="cost">$5,000</div>
                        <button class="menu-btn primary">Attend Conference</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Close</button>
            </div>
        `;
    }
    
    createOfficeModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">Office Management</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Manage your office space and facilities:</p>
                <div class="office-stats">
                    <div class="stat-card">
                        <div class="stat-value">250</div>
                        <div class="stat-label">Sq Ft</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Desks</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">85%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                </div>
                <div class="office-actions">
                    <button class="menu-btn primary">Expand Office</button>
                    <button class="menu-btn">Buy Equipment</button>
                    <button class="menu-btn">Upgrade Facilities</button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Close</button>
            </div>
        `;
    }
    
    createSettingsModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">Settings</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Audio</h3>
                    <label>Master Volume: <input type="range" min="0" max="100" value="50"></label>
                    <label>Sound Effects: <input type="range" min="0" max="100" value="75"></label>
                </div>
                <div class="settings-section">
                    <h3>Graphics</h3>
                    <label><input type="checkbox" checked> Enable Animations</label>
                    <label><input type="checkbox" checked> Show Particles</label>
                </div>
                <div class="settings-section">
                    <h3>Gameplay</h3>
                    <label><input type="checkbox" checked> Auto-save</label>
                    <label><input type="checkbox"> Show Tooltips</label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Cancel</button>
                <button class="menu-btn primary">Save Settings</button>
            </div>
        `;
    }
    
    createConfirmationModal(data, modalId) {
        return `
            <div class="modal-header">
                <h2 class="modal-title">${data.title || 'Confirm Action'}</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>${data.message || 'Are you sure you want to proceed?'}</p>
            </div>
            <div class="modal-footer">
                <button class="menu-btn btn-cancel">Cancel</button>
                <button class="menu-btn primary btn-confirm">${data.confirmText || 'Confirm'}</button>
            </div>
        `;
    }
    
    setupHireEmployeeHandlers(modal) {
        const element = modal.element;
        const employeeCards = element.querySelectorAll('.employee-card');
        const hireBtn = element.querySelector('.btn-hire');
        let selectedEmployee = null;
        
        employeeCards.forEach(card => {
            card.addEventListener('click', () => {
                // Remove previous selection
                employeeCards.forEach(c => c.classList.remove('selected'));
                
                // Select this card
                card.classList.add('selected');
                selectedEmployee = card.getAttribute('data-employee-id');
                
                // Enable hire button
                hireBtn.disabled = false;
            });
        });
        
        hireBtn.addEventListener('click', () => {
            if (selectedEmployee) {
                // Find the employee data and hire them
                const employeeSystem = window.gameEngine?.getSystem('employee');
                if (employeeSystem) {
                    // Create employee object from the selected card data
                    const selectedCard = element.querySelector(`[data-employee-id="${selectedEmployee}"]`);
                    const employeeName = selectedCard.querySelector('.employee-name').textContent;
                    const employeeRole = selectedCard.querySelector('.employee-role').textContent;
                    
                    // Generate a new employee with the selected attributes
                    const employee = employeeSystem.generateRandomEmployee();
                    employee.name = employeeName;
                    employee.roleName = employeeRole;
                    
                    const result = employeeSystem.hireEmployee(employee);
                    
                    if (result.success) {
                        this.eventSystem.emit('notification', {
                            type: 'success',
                            title: 'Employee Hired',
                            message: result.message
                        });
                        this.hide(modal.id);
                    } else {
                        this.eventSystem.emit('notification', {
                            type: 'error',
                            title: 'Hiring Failed',
                            message: result.message
                        });
                    }
                }
            }
        });
    }
    
    setupNewProjectHandlers(modal) {
        const element = modal.element;
        const projectTypeCards = element.querySelectorAll('.project-type-card');
        const createBtn = element.querySelector('.btn-create-project');
        let selectedType = null;
        
        projectTypeCards.forEach(card => {
            card.addEventListener('click', () => {
                projectTypeCards.forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                selectedType = card.getAttribute('data-type');
            });
        });
        
        createBtn.addEventListener('click', () => {
            const projectName = element.querySelector('#projectName').value;
            const projectBudget = element.querySelector('#projectBudget').value;
            const projectDeadline = element.querySelector('#projectDeadline').value;
            const projectDescription = element.querySelector('#projectDescription').value;
            
            if (!projectName || !selectedType) {
                this.eventSystem.emit('notification', {
                    type: 'error',
                    title: 'Invalid Input',
                    message: 'Please fill in all required fields'
                });
                return;
            }
            
            // Create project (this would be handled by ProjectSystem)
            this.eventSystem.emit('notification', {
                type: 'success',
                title: 'Project Created',
                message: `${projectName} project has been created!`
            });
            
            this.hide(modal.id);
        });
    }
    
    setupResearchHandlers(modal) {
        const element = modal.element;
        const researchNodes = element.querySelectorAll('.research-node.available');
        
        researchNodes.forEach(node => {
            node.addEventListener('click', () => {
                const title = node.querySelector('.research-title').textContent;
                const cost = node.querySelector('.research-cost').textContent;
                
                this.eventSystem.emit('notification', {
                    type: 'info',
                    title: 'Research Started',
                    message: `Started researching ${title} for ${cost}`
                });
            });
        });
    }
}
