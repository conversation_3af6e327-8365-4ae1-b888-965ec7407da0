/**
 * DevTycoon Pro - Employee System
 * Manages hiring, employee skills, satisfaction, and productivity
 */

class EmployeeSystem {
    constructor() {
        this.employees = [];
        this.eventSystem = null;
        this.companySystem = null;
        
        // Employee roles and their base salaries
        this.roles = {
            programmer: { name: 'Programmer', baseSalary: 4000, skills: ['coding', 'debugging'] },
            designer: { name: 'Designer', baseSalary: 3500, skills: ['design', 'creativity'] },
            tester: { name: 'Tester', baseSalary: 3000, skills: ['testing', 'attention'] },
            marketer: { name: 'Marketer', baseSalary: 3800, skills: ['marketing', 'communication'] },
            manager: { name: 'Manager', baseSalary: 5000, skills: ['leadership', 'organization'] }
        };
        
        // Skill categories
        this.skillCategories = {
            coding: 'Programming',
            debugging: 'Debugging',
            design: 'Design',
            creativity: 'Creativity',
            testing: 'Testing',
            attention: 'Attention to Detail',
            marketing: 'Marketing',
            communication: 'Communication',
            leadership: 'Leadership',
            organization: 'Organization'
        };
        
        this.nextEmployeeId = 1;
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.companySystem = gameEngine.getSystem('company');
        
        this.setupEventListeners();
        console.log('👥 Employee System initialized');
    }
    
    setupEventListeners() {
        this.eventSystem.on('newDay', (data) => {
            if (data.isWorkingDay) {
                this.processWorkDay();
            }
        });
        
        this.eventSystem.on('newMonth', () => {
            this.processMonthlyPayroll();
        });
    }
    
    generateRandomEmployee(role = null) {
        const roles = Object.keys(this.roles);
        const selectedRole = role || roles[Math.floor(Math.random() * roles.length)];
        const roleData = this.roles[selectedRole];
        
        const firstNames = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Riley', 'Avery', 'Quinn', 'Sage', 'River'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
        
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        
        const employee = {
            id: this.nextEmployeeId++,
            name: `${firstName} ${lastName}`,
            role: selectedRole,
            roleName: roleData.name,
            
            // Skills (0-100)
            skills: {},
            
            // Personal attributes
            salary: this.calculateSalary(roleData.baseSalary),
            happiness: Math.floor(Math.random() * 30) + 60, // 60-90
            productivity: Math.floor(Math.random() * 20) + 80, // 80-100
            experience: Math.floor(Math.random() * 10), // 0-10 years
            
            // Work status
            isWorking: false,
            currentProject: null,
            workload: 0, // 0-100%
            
            // Hire date
            hireDate: new Date(),
            
            // Performance tracking
            performanceHistory: [],
            totalContribution: 0
        };
        
        // Generate skills for this role
        roleData.skills.forEach(skill => {
            employee.skills[skill] = Math.floor(Math.random() * 40) + 40; // 40-80
        });
        
        // Add some random additional skills
        const allSkills = Object.keys(this.skillCategories);
        const additionalSkills = allSkills.filter(skill => !roleData.skills.includes(skill));
        const numAdditionalSkills = Math.floor(Math.random() * 3);
        
        for (let i = 0; i < numAdditionalSkills; i++) {
            const skill = additionalSkills[Math.floor(Math.random() * additionalSkills.length)];
            if (!employee.skills[skill]) {
                employee.skills[skill] = Math.floor(Math.random() * 30) + 20; // 20-50
            }
        }
        
        return employee;
    }
    
    calculateSalary(baseSalary) {
        // Add some randomness to salary
        const variation = 0.2; // ±20%
        const multiplier = 1 + (Math.random() - 0.5) * 2 * variation;
        return Math.round(baseSalary * multiplier);
    }
    
    hireEmployee(employee) {
        if (!this.companySystem.canAfford(employee.salary)) {
            return { success: false, message: 'Not enough money to hire this employee' };
        }
        
        employee.hireDate = new Date();
        this.employees.push(employee);
        
        // Update company
        this.companySystem.company.employeeCount = this.employees.length;
        this.companySystem.addExpense(employee.salary, 'Salary');
        
        this.eventSystem.emit('employeeHired', employee);
        this.updateUI();
        
        return { success: true, message: `${employee.name} has been hired!` };
    }
    
    fireEmployee(employeeId) {
        const index = this.employees.findIndex(emp => emp.id === employeeId);
        if (index === -1) return false;
        
        const employee = this.employees[index];
        this.employees.splice(index, 1);
        
        // Update company
        this.companySystem.company.employeeCount = this.employees.length;
        
        this.eventSystem.emit('employeeFired', employee);
        this.updateUI();
        
        return true;
    }
    
    getEmployee(employeeId) {
        return this.employees.find(emp => emp.id === employeeId);
    }
    
    getEmployeesByRole(role) {
        return this.employees.filter(emp => emp.role === role);
    }
    
    getAvailableEmployees() {
        return this.employees.filter(emp => !emp.isWorking);
    }
    
    assignToProject(employeeId, projectId) {
        const employee = this.getEmployee(employeeId);
        if (!employee || employee.isWorking) return false;
        
        employee.isWorking = true;
        employee.currentProject = projectId;
        employee.workload = 100;
        
        this.eventSystem.emit('employeeAssigned', { employee, projectId });
        return true;
    }
    
    unassignFromProject(employeeId) {
        const employee = this.getEmployee(employeeId);
        if (!employee) return false;
        
        employee.isWorking = false;
        employee.currentProject = null;
        employee.workload = 0;
        
        this.eventSystem.emit('employeeUnassigned', employee);
        return true;
    }
    
    processWorkDay() {
        this.employees.forEach(employee => {
            if (employee.isWorking) {
                // Calculate daily productivity
                const productivity = this.calculateDailyProductivity(employee);
                employee.totalContribution += productivity;
                
                // Update happiness based on workload and conditions
                this.updateEmployeeHappiness(employee);
                
                // Chance for skill improvement
                this.updateEmployeeSkills(employee);
            }
        });
        
        this.updateUI();
    }
    
    calculateDailyProductivity(employee) {
        let baseProductivity = employee.productivity;
        
        // Happiness affects productivity
        const happinessMultiplier = employee.happiness / 100;
        
        // Experience affects productivity
        const experienceMultiplier = 1 + (employee.experience * 0.05);
        
        // Workload affects productivity (overwork reduces efficiency)
        const workloadMultiplier = employee.workload > 80 ? 0.8 : 1.0;
        
        return baseProductivity * happinessMultiplier * experienceMultiplier * workloadMultiplier;
    }
    
    updateEmployeeHappiness(employee) {
        let happinessChange = 0;
        
        // Workload affects happiness
        if (employee.workload > 90) {
            happinessChange -= 2; // Overworked
        } else if (employee.workload < 50) {
            happinessChange -= 1; // Underutilized
        }
        
        // Random daily events
        if (Math.random() < 0.1) {
            happinessChange += Math.floor(Math.random() * 6) - 3; // -3 to +3
        }
        
        employee.happiness = Math.max(0, Math.min(100, employee.happiness + happinessChange));
    }
    
    updateEmployeeSkills(employee) {
        if (employee.isWorking && Math.random() < 0.1) { // 10% chance per day
            const roleSkills = this.roles[employee.role].skills;
            const skillToImprove = roleSkills[Math.floor(Math.random() * roleSkills.length)];
            
            if (employee.skills[skillToImprove] < 100) {
                employee.skills[skillToImprove] = Math.min(100, employee.skills[skillToImprove] + 1);
                
                this.eventSystem.emit('skillImproved', {
                    employee,
                    skill: skillToImprove,
                    newLevel: employee.skills[skillToImprove]
                });
            }
        }
    }
    
    processMonthlyPayroll() {
        let totalPayroll = 0;
        
        this.employees.forEach(employee => {
            totalPayroll += employee.salary;
        });
        
        if (totalPayroll > 0) {
            this.companySystem.addExpense(totalPayroll, 'Monthly Payroll');
            
            this.eventSystem.emit('payrollProcessed', {
                totalAmount: totalPayroll,
                employeeCount: this.employees.length
            });
        }
    }
    
    getTeamStats() {
        if (this.employees.length === 0) {
            return {
                averageHappiness: 0,
                averageProductivity: 0,
                totalSkillLevel: 0,
                employeeCount: 0
            };
        }
        
        const totalHappiness = this.employees.reduce((sum, emp) => sum + emp.happiness, 0);
        const totalProductivity = this.employees.reduce((sum, emp) => sum + emp.productivity, 0);
        const totalSkills = this.employees.reduce((sum, emp) => {
            return sum + Object.values(emp.skills).reduce((skillSum, skill) => skillSum + skill, 0);
        }, 0);
        
        return {
            averageHappiness: Math.round(totalHappiness / this.employees.length),
            averageProductivity: Math.round(totalProductivity / this.employees.length),
            totalSkillLevel: Math.round(totalSkills / this.employees.length),
            employeeCount: this.employees.length
        };
    }
    
    updateUI() {
        // Update employee count display
        const employeeCountDisplay = document.getElementById('employeeCountDisplay');
        if (employeeCountDisplay) {
            employeeCountDisplay.textContent = `👥 ${this.employees.length}`;
        }
        
        // Update employees list
        const employeesList = document.getElementById('employeesList');
        if (employeesList) {
            if (this.employees.length === 0) {
                employeesList.innerHTML = '<p class="no-employees">No employees hired</p>';
            } else {
                employeesList.innerHTML = this.employees.map(emp => `
                    <div class="employee-item">
                        <div class="employee-name">${emp.name}</div>
                        <div class="employee-role">${emp.roleName}</div>
                        <div class="employee-happiness">😊 ${emp.happiness}%</div>
                    </div>
                `).join('');
            }
        }
        
        // Emit UI update event
        this.eventSystem.emit('uiUpdate', {
            type: 'employees',
            employees: this.employees,
            stats: this.getTeamStats()
        });
    }
    
    // Save/Load
    getSaveData() {
        return {
            employees: this.employees,
            nextEmployeeId: this.nextEmployeeId
        };
    }
    
    loadSaveData(data) {
        this.employees = data.employees || [];
        this.nextEmployeeId = data.nextEmployeeId || 1;
        
        this.updateUI();
    }
}
