/**
 * DevTycoon Pro - Save System
 * Handles game save and load functionality
 */

class SaveSystem {
    constructor() {
        this.eventSystem = null;
        this.gameEngine = null;
        this.autoSaveInterval = 300000; // 5 minutes
        this.autoSaveTimer = null;
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.gameEngine = gameEngine;
        
        this.startAutoSave();
        console.log('💾 Save System initialized');
    }
    
    startAutoSave() {
        this.autoSaveTimer = setInterval(() => {
            this.autoSave();
        }, this.autoSaveInterval);
    }
    
    async quickSave() {
        const saveData = this.createSaveData();
        localStorage.setItem('devtycoon_quicksave', JSON.stringify(saveData));
        console.log('💾 Quick save completed');
    }
    
    async quickLoad() {
        const saveData = localStorage.getItem('devtycoon_quicksave');
        if (saveData) {
            this.loadSaveData(JSON.parse(saveData));
            console.log('📁 Quick load completed');
        }
    }
    
    autoSave() {
        if (this.gameEngine.gameState === 'playing') {
            const saveData = this.createSaveData();
            localStorage.setItem('devtycoon_autosave', JSON.stringify(saveData));
            console.log('💾 Auto save completed');
        }
    }
    
    createSaveData() {
        const saveData = {
            version: '1.0.0',
            timestamp: Date.now(),
            gameState: this.gameEngine.gameState
        };
        
        // Collect save data from all systems
        this.gameEngine.systems.forEach((system, name) => {
            if (system.getSaveData) {
                saveData[name] = system.getSaveData();
            }
        });
        
        return saveData;
    }
    
    loadSaveData(saveData) {
        // Load data into all systems
        this.gameEngine.systems.forEach((system, name) => {
            if (system.loadSaveData && saveData[name]) {
                system.loadSaveData(saveData[name]);
            }
        });
        
        // Restore game state
        if (saveData.gameState) {
            this.gameEngine.gameState = saveData.gameState;
        }
    }
}
