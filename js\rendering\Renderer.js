/**
 * DevTycoon Pro - Renderer
 * Handles 2D canvas rendering
 */

class Renderer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.eventSystem = null;
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.canvas = document.getElementById('gameCanvas');
        
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            this.setupCanvas();
        }
        
        console.log('🎨 Renderer initialized');
    }
    
    setupCanvas() {
        // Set up canvas for high DPI displays
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    render() {
        if (!this.ctx) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render placeholder office view
        this.renderOffice();
    }
    
    renderOffice() {
        const ctx = this.ctx;
        const width = this.canvas.clientWidth;
        const height = this.canvas.clientHeight;
        
        // Draw office background
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, width, height);
        
        // Draw office grid
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 1;
        
        const gridSize = 20;
        for (let x = 0; x < width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        
        for (let y = 0; y < height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // Draw placeholder desks
        ctx.fillStyle = '#8B4513';
        for (let i = 0; i < 5; i++) {
            const x = 50 + (i * 120);
            const y = 100;
            ctx.fillRect(x, y, 80, 40);
        }
        
        // Draw placeholder text
        ctx.fillStyle = '#333';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Office View - Coming Soon!', width / 2, height / 2);
        ctx.fillText('Hire employees and start projects to see activity here', width / 2, height / 2 + 30);
    }
}
