/**
 * DevTycoon Pro - Game Data
 * Contains game configuration and static data
 */

class GameData {
    constructor() {
        this.loaded = false;
        this.version = '1.0.0';
        
        // Game configuration
        this.config = {
            startingYear: 2000,
            maxEmployees: 1000,
            maxProjects: 50,
            autoSaveInterval: 300000 // 5 minutes
        };
        
        // Technology data
        this.technologies = {
            programming: ['JavaScript', 'Python', 'Java', 'C++', 'C#'],
            frameworks: ['React', 'Angular', 'Vue', 'Django', 'Spring'],
            databases: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis'],
            platforms: ['Web', 'Mobile', 'Desktop', 'Cloud']
        };
        
        // Market data
        this.marketSegments = [
            'Web Development',
            'Mobile Apps',
            'Enterprise Software',
            'Games',
            'AI/ML',
            'E-commerce'
        ];
    }
    
    async load() {
        // In a real implementation, this would load data from files
        this.loaded = true;
        console.log('📊 Game data loaded');
    }
    
    getTechnology(category, name) {
        return this.technologies[category]?.includes(name);
    }
    
    getMarketSegments() {
        return [...this.marketSegments];
    }
}
