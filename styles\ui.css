/* DevTycoon Pro - UI Components */

/* Modal Styles */
.modal {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    border-radius: 16px;
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-title {
    font-size: 1.8rem;
    color: #ffffff;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    color: #ffffff;
}

.modal-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Employee Hiring Modal */
.employee-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.employee-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.employee-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.employee-card.selected {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.employee-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.employee-role {
    color: #3498db;
    font-weight: 500;
    margin-bottom: 1rem;
}

.employee-skills {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.skill-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.skill-name {
    font-size: 0.9rem;
}

.skill-bar {
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.skill-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    transition: width 0.3s ease;
}

.employee-salary {
    font-weight: 600;
    color: #f39c12;
    text-align: center;
}

/* Project Creation Modal */
.project-form {
    display: grid;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-field label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #3498db;
}

.form-field input,
.form-field select,
.form-field textarea {
    padding: 0.8rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
}

.form-field textarea {
    resize: vertical;
    min-height: 100px;
}

.project-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.project-type-card {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.project-type-card:hover {
    border-color: #3498db;
}

.project-type-card.selected {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.project-type-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.project-type-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-type-desc {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Research Modal */
.research-tree {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.research-node {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #34495e;
    position: relative;
    transition: all 0.3s ease;
}

.research-node.available {
    border-color: #3498db;
    cursor: pointer;
}

.research-node.available:hover {
    border-color: #2980b9;
    transform: translateY(-2px);
}

.research-node.completed {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.research-node.locked {
    opacity: 0.5;
}

.research-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.research-desc {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.research-cost {
    color: #f39c12;
    font-weight: 600;
}

.research-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
}

/* Office Layout Modal */
.office-grid {
    display: grid;
    grid-template-columns: repeat(20, 1fr);
    grid-template-rows: repeat(15, 1fr);
    gap: 1px;
    background: #34495e;
    border: 2px solid #3498db;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    min-height: 400px;
}

.office-cell {
    background: #2c3e50;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.office-cell:hover {
    background: #3498db;
}

.office-cell.wall {
    background: #7f8c8d;
}

.office-cell.desk {
    background: #e67e22;
}

.office-cell.meeting-room {
    background: #9b59b6;
}

.office-cell.break-room {
    background: #1abc9c;
}

.office-cell.server-room {
    background: #e74c3c;
}

/* Furniture Palette */
.furniture-palette {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.furniture-item {
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.furniture-item:hover {
    border-color: #3498db;
}

.furniture-item.selected {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

/* Statistics Panel */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stat-change {
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

/* Notification Styles */
.notification {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.notification:hover {
    transform: translateX(4px);
}

.notification.success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.notification.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.notification.error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.notification-message {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
