/* DevTycoon Pro - Animations */

/* Keyframe Animations */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scaleOut {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0.8);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-4px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(4px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        transform: rotate(-180deg) scale(0.5);
        opacity: 0;
    }
    to {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease forwards;
}

.animate-slide-in {
    animation: slideIn 0.4s ease forwards;
}

.animate-slide-out {
    animation: slideOut 0.3s ease forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease forwards;
}

.animate-scale-out {
    animation: scaleOut 0.2s ease forwards;
}

.animate-bounce {
    animation: bounce 0.6s ease;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease;
}

.animate-glow {
    animation: glow 2s infinite;
}

.animate-slide-up {
    animation: slideUp 0.4s ease forwards;
}

.animate-slide-down {
    animation: slideDown 0.4s ease forwards;
}

.animate-rotate-in {
    animation: rotateIn 0.5s ease forwards;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Transition Classes */
.transition-all {
    transition: all 0.3s ease;
}

.transition-fast {
    transition: all 0.15s ease;
}

.transition-slow {
    transition: all 0.6s ease;
}

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.6);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Progress Animations */
.progress-bar-animated .progress-fill {
    background: linear-gradient(90deg, #3498db, #2980b9, #3498db);
    background-size: 200% 100%;
    animation: progressShine 2s linear infinite;
}

@keyframes progressShine {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Particle Effects */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #3498db;
    border-radius: 50%;
    pointer-events: none;
    animation: particle 3s linear infinite;
}

@keyframes particle {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(0);
        opacity: 0;
    }
}

/* Money Animation */
.money-popup {
    position: absolute;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.2rem;
    pointer-events: none;
    animation: moneyFloat 2s ease-out forwards;
    z-index: 1000;
}

@keyframes moneyFloat {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(1.2);
        opacity: 0;
    }
}

/* Screen Transitions */
.screen-transition-enter {
    animation: fadeIn 0.5s ease forwards;
}

.screen-transition-exit {
    animation: fadeOut 0.3s ease forwards;
}

/* Button Press Effect */
.btn-press {
    animation: btnPress 0.1s ease;
}

@keyframes btnPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* Notification Animations */
.notification-enter {
    animation: slideIn 0.3s ease forwards;
}

.notification-exit {
    animation: slideOut 0.3s ease forwards;
}

/* Modal Animations */
.modal-enter {
    animation: scaleIn 0.3s ease forwards;
}

.modal-exit {
    animation: scaleOut 0.2s ease forwards;
}

/* Success/Error Feedback */
.success-flash {
    animation: successFlash 0.6s ease;
}

@keyframes successFlash {
    0%, 100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(39, 174, 96, 0.3);
    }
}

.error-flash {
    animation: errorFlash 0.6s ease;
}

@keyframes errorFlash {
    0%, 100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(231, 76, 60, 0.3);
    }
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
