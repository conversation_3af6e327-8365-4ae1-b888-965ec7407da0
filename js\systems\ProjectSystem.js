/**
 * DevTycoon Pro - Project System
 * Manages software development projects
 */

class ProjectSystem {
    constructor() {
        this.projects = [];
        this.eventSystem = null;
        this.nextProjectId = 1;
        
        // Project types and their characteristics
        this.projectTypes = {
            website: {
                name: 'Website',
                baseTime: 30, // days
                baseCost: 5000,
                baseRevenue: 8000,
                complexity: 1,
                requiredSkills: ['coding', 'design']
            },
            webapp: {
                name: 'Web Application',
                baseTime: 60,
                baseCost: 15000,
                baseRevenue: 25000,
                complexity: 2,
                requiredSkills: ['coding', 'design', 'testing']
            },
            mobile: {
                name: 'Mobile App',
                baseTime: 45,
                baseCost: 12000,
                baseRevenue: 20000,
                complexity: 2,
                requiredSkills: ['coding', 'design', 'testing']
            },
            game: {
                name: 'Game',
                baseTime: 90,
                baseCost: 25000,
                baseRevenue: 40000,
                complexity: 3,
                requiredSkills: ['coding', 'design', 'creativity']
            }
        };
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.setupEventListeners();
        console.log('📁 Project System initialized');
    }
    
    setupEventListeners() {
        this.eventSystem.on('newDay', (data) => {
            if (data.isWorkingDay) {
                this.updateProjects();
            }
        });
    }
    
    createProject(projectData) {
        const projectType = this.projectTypes[projectData.type];
        if (!projectType) {
            return { success: false, message: 'Invalid project type' };
        }
        
        const project = {
            id: this.nextProjectId++,
            name: projectData.name,
            type: projectData.type,
            typeName: projectType.name,
            description: projectData.description || '',
            
            // Timeline
            startDate: new Date(),
            estimatedDays: projectType.baseTime,
            deadline: projectData.deadline || projectType.baseTime,
            daysWorked: 0,
            
            // Progress
            progress: 0, // 0-100%
            phase: 'planning', // planning, development, testing, deployment
            
            // Financial
            budget: projectData.budget || projectType.baseCost,
            estimatedRevenue: projectType.baseRevenue,
            actualCost: 0,
            
            // Team
            assignedEmployees: [],
            requiredSkills: [...projectType.requiredSkills],
            
            // Quality metrics
            qualityScore: 0,
            bugCount: 0,
            
            // Status
            status: 'active', // active, completed, cancelled, failed
            
            // Complexity and requirements
            complexity: projectType.complexity,
            features: [],
            
            // Client information (if applicable)
            client: projectData.client || null,
            isContract: !!projectData.client
        };
        
        this.projects.push(project);
        this.eventSystem.emit('projectCreated', project);
        this.updateUI();
        
        return { success: true, project };
    }
    
    assignEmployee(projectId, employeeId) {
        const project = this.getProject(projectId);
        if (!project) return false;
        
        if (project.assignedEmployees.includes(employeeId)) {
            return false; // Already assigned
        }
        
        project.assignedEmployees.push(employeeId);
        this.eventSystem.emit('employeeAssignedToProject', { projectId, employeeId });
        
        return true;
    }
    
    unassignEmployee(projectId, employeeId) {
        const project = this.getProject(projectId);
        if (!project) return false;
        
        const index = project.assignedEmployees.indexOf(employeeId);
        if (index === -1) return false;
        
        project.assignedEmployees.splice(index, 1);
        this.eventSystem.emit('employeeUnassignedFromProject', { projectId, employeeId });
        
        return true;
    }
    
    updateProjects() {
        this.projects.forEach(project => {
            if (project.status === 'active') {
                this.updateProject(project);
            }
        });
        
        this.updateUI();
    }
    
    updateProject(project) {
        if (project.assignedEmployees.length === 0) {
            return; // No progress without employees
        }
        
        // Calculate daily progress based on team productivity
        const dailyProgress = this.calculateDailyProgress(project);
        
        project.progress = Math.min(100, project.progress + dailyProgress);
        project.daysWorked++;
        
        // Update project phase based on progress
        this.updateProjectPhase(project);
        
        // Calculate quality and bugs
        this.updateProjectQuality(project);
        
        // Check for completion
        if (project.progress >= 100) {
            this.completeProject(project);
        }
        
        // Check for deadline issues
        this.checkDeadlines(project);
    }
    
    calculateDailyProgress(project) {
        const employeeSystem = window.gameEngine?.getSystem('employee');
        if (!employeeSystem) return 1; // Fallback
        
        let totalProductivity = 0;
        let teamSize = 0;
        
        project.assignedEmployees.forEach(employeeId => {
            const employee = employeeSystem.getEmployee(employeeId);
            if (employee) {
                // Calculate employee contribution based on relevant skills
                let skillMatch = 0;
                project.requiredSkills.forEach(skill => {
                    if (employee.skills[skill]) {
                        skillMatch += employee.skills[skill];
                    }
                });
                
                const avgSkill = skillMatch / project.requiredSkills.length;
                const productivity = (employee.productivity / 100) * (avgSkill / 100);
                
                totalProductivity += productivity;
                teamSize++;
            }
        });
        
        if (teamSize === 0) return 0;
        
        // Base progress per day (adjusted for complexity)
        const baseProgress = 2 / project.complexity;
        
        // Team efficiency multiplier
        const teamEfficiency = totalProductivity / teamSize;
        
        return baseProgress * teamEfficiency;
    }
    
    updateProjectPhase(project) {
        const oldPhase = project.phase;
        
        if (project.progress < 25) {
            project.phase = 'planning';
        } else if (project.progress < 75) {
            project.phase = 'development';
        } else if (project.progress < 95) {
            project.phase = 'testing';
        } else {
            project.phase = 'deployment';
        }
        
        if (project.phase !== oldPhase) {
            this.eventSystem.emit('projectPhaseChanged', {
                project,
                oldPhase,
                newPhase: project.phase
            });
        }
    }
    
    updateProjectQuality(project) {
        const employeeSystem = window.gameEngine?.getSystem('employee');
        if (!employeeSystem) return;
        
        let totalQuality = 0;
        let teamSize = 0;
        
        project.assignedEmployees.forEach(employeeId => {
            const employee = employeeSystem.getEmployee(employeeId);
            if (employee) {
                // Quality based on relevant skills and happiness
                let skillQuality = 0;
                project.requiredSkills.forEach(skill => {
                    if (employee.skills[skill]) {
                        skillQuality += employee.skills[skill];
                    }
                });
                
                const avgSkill = skillQuality / project.requiredSkills.length;
                const happinessMultiplier = employee.happiness / 100;
                
                totalQuality += avgSkill * happinessMultiplier;
                teamSize++;
            }
        });
        
        if (teamSize > 0) {
            project.qualityScore = totalQuality / teamSize;
            
            // Generate bugs based on quality (lower quality = more bugs)
            const bugChance = (100 - project.qualityScore) / 1000;
            if (Math.random() < bugChance) {
                project.bugCount++;
                
                this.eventSystem.emit('bugFound', {
                    project,
                    bugCount: project.bugCount
                });
            }
        }
    }
    
    completeProject(project) {
        project.status = 'completed';
        project.progress = 100;
        
        // Calculate final revenue based on quality and deadline performance
        const qualityMultiplier = project.qualityScore / 100;
        const deadlineMultiplier = project.daysWorked <= project.deadline ? 1.2 : 0.8;
        
        project.actualRevenue = Math.round(
            project.estimatedRevenue * qualityMultiplier * deadlineMultiplier
        );
        
        // Add revenue to company
        const companySystem = window.gameEngine?.getSystem('company');
        if (companySystem) {
            companySystem.addIncome(project.actualRevenue, 'Project Revenue');
        }
        
        this.eventSystem.emit('projectCompleted', project);
        
        // Show completion notification
        this.eventSystem.emit('notification', {
            type: 'success',
            title: 'Project Completed!',
            message: `${project.name} finished with ${Math.round(project.qualityScore)}% quality`
        });
    }
    
    checkDeadlines(project) {
        const daysRemaining = project.deadline - project.daysWorked;
        
        if (daysRemaining <= 5 && daysRemaining > 0) {
            this.eventSystem.emit('notification', {
                type: 'warning',
                title: 'Deadline Approaching',
                message: `${project.name} deadline in ${daysRemaining} days`
            });
        } else if (daysRemaining <= 0 && project.status === 'active') {
            this.eventSystem.emit('notification', {
                type: 'error',
                title: 'Deadline Missed',
                message: `${project.name} has missed its deadline`
            });
        }
    }
    
    cancelProject(projectId) {
        const project = this.getProject(projectId);
        if (!project) return false;
        
        project.status = 'cancelled';
        
        // Unassign all employees
        project.assignedEmployees.forEach(employeeId => {
            const employeeSystem = window.gameEngine?.getSystem('employee');
            if (employeeSystem) {
                employeeSystem.unassignFromProject(employeeId);
            }
        });
        
        this.eventSystem.emit('projectCancelled', project);
        this.updateUI();
        
        return true;
    }
    
    getProject(projectId) {
        return this.projects.find(p => p.id === projectId);
    }
    
    getActiveProjects() {
        return this.projects.filter(p => p.status === 'active');
    }
    
    getCompletedProjects() {
        return this.projects.filter(p => p.status === 'completed');
    }
    
    getProjectStats() {
        const active = this.getActiveProjects();
        const completed = this.getCompletedProjects();
        
        return {
            totalProjects: this.projects.length,
            activeProjects: active.length,
            completedProjects: completed.length,
            totalRevenue: completed.reduce((sum, p) => sum + (p.actualRevenue || 0), 0),
            averageQuality: completed.length > 0 
                ? completed.reduce((sum, p) => sum + p.qualityScore, 0) / completed.length 
                : 0
        };
    }
    
    updateUI() {
        const activeProjects = this.getActiveProjects();
        
        // Update active projects list
        const activeProjectsList = document.getElementById('activeProjectsList');
        if (activeProjectsList) {
            if (activeProjects.length === 0) {
                activeProjectsList.innerHTML = '<p class="no-projects">No active projects</p>';
            } else {
                activeProjectsList.innerHTML = activeProjects.map(project => `
                    <div class="project-item" data-project-id="${project.id}">
                        <div class="project-header">
                            <span class="project-name">${project.name}</span>
                            <span class="project-phase">${project.phase}</span>
                        </div>
                        <div class="project-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${project.progress}%"></div>
                            </div>
                            <span class="progress-text">${Math.round(project.progress)}%</span>
                        </div>
                        <div class="project-details">
                            <span class="project-team">👥 ${project.assignedEmployees.length}</span>
                            <span class="project-deadline">📅 ${project.deadline - project.daysWorked} days</span>
                        </div>
                    </div>
                `).join('');
            }
        }
        
        // Emit UI update event
        this.eventSystem.emit('uiUpdate', {
            type: 'projects',
            projects: activeProjects,
            stats: this.getProjectStats()
        });
    }
    
    // Save/Load
    getSaveData() {
        return {
            projects: this.projects,
            nextProjectId: this.nextProjectId
        };
    }
    
    loadSaveData(data) {
        this.projects = data.projects || [];
        this.nextProjectId = data.nextProjectId || 1;
        
        // Convert date strings back to Date objects
        this.projects.forEach(project => {
            if (typeof project.startDate === 'string') {
                project.startDate = new Date(project.startDate);
            }
        });
        
        this.updateUI();
    }
}
