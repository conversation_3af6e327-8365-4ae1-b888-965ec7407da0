/**
 * DevTycoon Pro - Core Game Engine
 * Manages the main game loop, state, and system coordination
 */

class GameEngine {
    constructor() {
        this.isRunning = false;
        this.isPaused = false;
        this.gameSpeed = 1;
        this.lastFrameTime = 0;
        this.deltaTime = 0;
        this.frameCount = 0;
        this.fps = 60;
        
        // Game state
        this.gameState = 'loading'; // loading, menu, setup, playing, paused
        this.currentScreen = 'loadingScreen';
        
        // Systems
        this.systems = new Map();
        this.eventSystem = null;
        this.timeManager = null;
        this.saveSystem = null;
        this.uiManager = null;
        this.renderer = null;
        
        // Game data
        this.gameData = null;
        this.company = null;
        
        // Performance monitoring
        this.performanceStats = {
            frameTime: 0,
            updateTime: 0,
            renderTime: 0,
            memoryUsage: 0
        };
        
        this.init();
    }
    
    async init() {
        console.log('🎮 Initializing DevTycoon Pro Game Engine...');
        
        try {
            // Initialize core systems
            await this.initializeSystems();
            
            // Load game data
            await this.loadGameData();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Start the game loop
            this.start();
            
            console.log('✅ Game Engine initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize game engine:', error);
            this.handleInitializationError(error);
        }
    }
    
    async initializeSystems() {
        // Initialize event system first
        this.eventSystem = new EventSystem();
        this.systems.set('events', this.eventSystem);
        
        // Initialize time manager
        this.timeManager = new TimeManager();
        this.systems.set('time', this.timeManager);
        
        // Initialize save system
        this.saveSystem = new SaveSystem();
        this.systems.set('save', this.saveSystem);
        
        // Initialize UI manager
        this.uiManager = new UIManager();
        this.systems.set('ui', this.uiManager);
        
        // Initialize renderer
        this.renderer = new Renderer();
        this.systems.set('renderer', this.renderer);
        
        // Initialize game systems
        this.systems.set('company', new CompanySystem());
        this.systems.set('employee', new EmployeeSystem());
        this.systems.set('project', new ProjectSystem());
        this.systems.set('market', new MarketSystem());
        this.systems.set('research', new ResearchSystem());
        this.systems.set('office', new OfficeSystem());
        
        // Initialize all systems
        for (const [name, system] of this.systems) {
            if (system.init) {
                await system.init(this);
                console.log(`✅ ${name} system initialized`);
            }
        }
    }
    
    async loadGameData() {
        this.gameData = new GameData();
        await this.gameData.load();
        console.log('✅ Game data loaded');
    }
    
    setupEventListeners() {
        // Window events
        window.addEventListener('beforeunload', () => {
            this.autoSave();
        });
        
        window.addEventListener('blur', () => {
            if (this.gameState === 'playing') {
                this.pause();
            }
        });
        
        window.addEventListener('focus', () => {
            if (this.gameState === 'paused') {
                this.resume();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
        
        // Game events
        this.eventSystem.on('gameStateChange', (newState) => {
            this.onGameStateChange(newState);
        });
        
        this.eventSystem.on('screenChange', (newScreen) => {
            this.switchScreen(newScreen);
        });
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.lastFrameTime = performance.now();
        this.gameLoop();
        
        console.log('🚀 Game loop started');
    }
    
    stop() {
        this.isRunning = false;
        console.log('⏹️ Game loop stopped');
    }
    
    pause() {
        if (this.gameState !== 'playing') return;
        
        this.isPaused = true;
        this.gameState = 'paused';
        this.eventSystem.emit('gamePaused');
        console.log('⏸️ Game paused');
    }
    
    resume() {
        if (this.gameState !== 'paused') return;
        
        this.isPaused = false;
        this.gameState = 'playing';
        this.lastFrameTime = performance.now();
        this.eventSystem.emit('gameResumed');
        console.log('▶️ Game resumed');
    }
    
    setGameSpeed(speed) {
        this.gameSpeed = Math.max(0.1, Math.min(5, speed));
        this.eventSystem.emit('gameSpeedChanged', this.gameSpeed);
        console.log(`⚡ Game speed set to ${this.gameSpeed}x`);
    }
    
    gameLoop() {
        if (!this.isRunning) return;
        
        const currentTime = performance.now();
        this.deltaTime = (currentTime - this.lastFrameTime) / 1000;
        this.lastFrameTime = currentTime;
        
        // Cap delta time to prevent spiral of death
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        // Apply game speed
        const adjustedDeltaTime = this.deltaTime * this.gameSpeed;
        
        try {
            // Update performance stats
            const updateStart = performance.now();
            
            // Update systems
            if (!this.isPaused) {
                this.update(adjustedDeltaTime);
            }
            
            const updateEnd = performance.now();
            
            // Render
            const renderStart = performance.now();
            this.render();
            const renderEnd = performance.now();
            
            // Update performance stats
            this.updatePerformanceStats(updateEnd - updateStart, renderEnd - renderStart);
            
        } catch (error) {
            console.error('❌ Error in game loop:', error);
            this.handleGameLoopError(error);
        }
        
        this.frameCount++;
        requestAnimationFrame(() => this.gameLoop());
    }
    
    update(deltaTime) {
        // Update time manager first
        this.timeManager.update(deltaTime);
        
        // Update all other systems
        for (const [name, system] of this.systems) {
            if (name !== 'time' && system.update) {
                system.update(deltaTime);
            }
        }
        
        // Update UI
        this.uiManager.update(deltaTime);
    }
    
    render() {
        // Clear and render main game
        this.renderer.render();
        
        // Render UI overlay
        this.uiManager.render();
    }
    
    switchScreen(screenId) {
        const screens = document.querySelectorAll('.screen');
        screens.forEach(screen => {
            screen.classList.remove('active');
        });
        
        const newScreen = document.getElementById(screenId);
        if (newScreen) {
            newScreen.classList.add('active');
            this.currentScreen = screenId;
            this.eventSystem.emit('screenChanged', screenId);
        }
    }
    
    onGameStateChange(newState) {
        const oldState = this.gameState;
        this.gameState = newState;
        
        console.log(`🔄 Game state changed: ${oldState} → ${newState}`);
        
        // Handle state-specific logic
        switch (newState) {
            case 'playing':
                this.switchScreen('gameScreen');
                break;
            case 'menu':
                this.switchScreen('mainMenu');
                break;
            case 'setup':
                this.switchScreen('companySetup');
                break;
        }
    }
    
    handleKeyboard(event) {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 's':
                    event.preventDefault();
                    this.quickSave();
                    break;
                case 'l':
                    event.preventDefault();
                    this.quickLoad();
                    break;
            }
        }
        
        switch (event.key) {
            case ' ':
                if (this.gameState === 'playing' || this.gameState === 'paused') {
                    event.preventDefault();
                    this.isPaused ? this.resume() : this.pause();
                }
                break;
            case 'Escape':
                if (this.gameState === 'playing') {
                    this.pause();
                }
                break;
        }
    }
    
    updatePerformanceStats(updateTime, renderTime) {
        this.performanceStats.frameTime = this.deltaTime * 1000;
        this.performanceStats.updateTime = updateTime;
        this.performanceStats.renderTime = renderTime;
        
        // Update memory usage periodically
        if (this.frameCount % 60 === 0 && performance.memory) {
            this.performanceStats.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        }
    }
    
    getSystem(name) {
        return this.systems.get(name);
    }
    
    async quickSave() {
        try {
            await this.saveSystem.quickSave();
            this.eventSystem.emit('notification', {
                type: 'success',
                title: 'Game Saved',
                message: 'Your progress has been saved successfully.'
            });
        } catch (error) {
            console.error('Failed to save game:', error);
            this.eventSystem.emit('notification', {
                type: 'error',
                title: 'Save Failed',
                message: 'Could not save your progress.'
            });
        }
    }
    
    async quickLoad() {
        try {
            await this.saveSystem.quickLoad();
            this.eventSystem.emit('notification', {
                type: 'success',
                title: 'Game Loaded',
                message: 'Your save has been loaded successfully.'
            });
        } catch (error) {
            console.error('Failed to load game:', error);
            this.eventSystem.emit('notification', {
                type: 'error',
                title: 'Load Failed',
                message: 'Could not load your save file.'
            });
        }
    }
    
    autoSave() {
        if (this.gameState === 'playing') {
            this.saveSystem.autoSave();
        }
    }
    
    handleInitializationError(error) {
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: #1a1a1a; color: white; font-family: Arial, sans-serif;">
                <div style="text-align: center; max-width: 500px; padding: 2rem;">
                    <h1 style="color: #e74c3c; margin-bottom: 1rem;">Game Initialization Failed</h1>
                    <p style="margin-bottom: 1rem;">Sorry, the game failed to initialize properly.</p>
                    <p style="font-size: 0.9rem; color: #7f8c8d;">${error.message}</p>
                    <button onclick="location.reload()" style="margin-top: 2rem; padding: 1rem 2rem; background: #3498db; color: white; border: none; border-radius: 8px; cursor: pointer;">
                        Reload Game
                    </button>
                </div>
            </div>
        `;
    }
    
    handleGameLoopError(error) {
        console.error('Game loop error:', error);
        this.pause();
        
        this.eventSystem.emit('notification', {
            type: 'error',
            title: 'Game Error',
            message: 'An error occurred. The game has been paused.'
        });
    }
}

// Global game engine instance
window.gameEngine = null;
