/**
 * DevTycoon Pro - Office System
 * Manages office space and facilities
 */

class OfficeSystem {
    constructor() {
        this.eventSystem = null;
        this.office = {
            size: 250, // square feet
            desks: 5,
            efficiency: 85,
            facilities: []
        };
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        console.log('🏢 Office System initialized');
    }
    
    expandOffice(additionalSize) {
        this.office.size += additionalSize;
        this.eventSystem.emit('officeExpanded', { newSize: this.office.size });
    }
    
    getSaveData() {
        return { office: this.office };
    }
    
    loadSaveData(data) {
        this.office = data.office || this.office;
    }
}
