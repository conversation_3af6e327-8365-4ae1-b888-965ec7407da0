<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevTycoon Pro - Advanced Software Development Game</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <div id="gameContainer">
        <!-- Loading Screen -->
        <div id="loadingScreen" class="screen active">
            <div class="loading-content">
                <h1>DevTycoon Pro</h1>
                <div class="loading-bar">
                    <div class="loading-progress" id="loadingProgress"></div>
                </div>
                <p id="loadingText">Initializing game systems...</p>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="mainMenu" class="screen">
            <div class="menu-content">
                <h1 class="game-title">DevTycoon Pro</h1>
                <p class="game-subtitle">Build Your Software Empire</p>
                <div class="menu-buttons">
                    <button id="newGameBtn" class="menu-btn primary">New Game</button>
                    <button id="loadGameBtn" class="menu-btn">Load Game</button>
                    <button id="settingsBtn" class="menu-btn">Settings</button>
                    <button id="creditsBtn" class="menu-btn">Credits</button>
                </div>
            </div>
        </div>

        <!-- Company Setup Screen -->
        <div id="companySetup" class="screen">
            <div class="setup-content">
                <h2>Create Your Company</h2>
                <div class="setup-form">
                    <div class="form-group">
                        <label for="companyName">Company Name:</label>
                        <input type="text" id="companyName" placeholder="Enter company name" maxlength="30">
                    </div>
                    <div class="form-group">
                        <label for="founderName">Founder Name:</label>
                        <input type="text" id="founderName" placeholder="Enter your name" maxlength="25">
                    </div>
                    <div class="form-group">
                        <label>Starting Difficulty:</label>
                        <select id="difficulty">
                            <option value="easy">Easy - $50,000 starting capital</option>
                            <option value="normal" selected>Normal - $25,000 starting capital</option>
                            <option value="hard">Hard - $10,000 starting capital</option>
                            <option value="expert">Expert - $5,000 starting capital</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Starting Year:</label>
                        <select id="startYear">
                            <option value="1980">1980 - Dawn of Personal Computing</option>
                            <option value="1990">1990 - GUI Revolution</option>
                            <option value="2000" selected>2000 - Internet Boom</option>
                            <option value="2010">2010 - Mobile Era</option>
                            <option value="2020">2020 - Cloud & AI</option>
                        </select>
                    </div>
                    <button id="startGameBtn" class="menu-btn primary">Start Company</button>
                    <button id="backToMenuBtn" class="menu-btn">Back</button>
                </div>
            </div>
        </div>

        <!-- Main Game Screen -->
        <div id="gameScreen" class="screen">
            <!-- Top UI Bar -->
            <div id="topBar" class="ui-bar">
                <div class="company-info">
                    <span id="companyNameDisplay"></span>
                    <span id="reputationDisplay">★ 0</span>
                </div>
                <div class="game-stats">
                    <span id="dateDisplay"></span>
                    <span id="cashDisplay">$0</span>
                    <span id="employeeCountDisplay">👥 0</span>
                </div>
                <div class="game-controls">
                    <button id="pauseBtn" class="control-btn">⏸️</button>
                    <button id="speedBtn" class="control-btn" data-speed="1">1x</button>
                    <button id="menuBtn" class="control-btn">☰</button>
                </div>
            </div>

            <!-- Main Game Area -->
            <div id="gameArea">
                <!-- Left Sidebar -->
                <div id="leftSidebar" class="sidebar">
                    <div class="sidebar-section">
                        <h3>Quick Actions</h3>
                        <button class="action-btn" id="hireEmployeeBtn">👥 Hire Employee</button>
                        <button class="action-btn" id="newProjectBtn">📁 New Project</button>
                        <button class="action-btn" id="researchBtn">🔬 Research</button>
                        <button class="action-btn" id="marketingBtn">📢 Marketing</button>
                        <button class="action-btn" id="officeBtn">🏢 Office</button>
                    </div>
                    <div class="sidebar-section">
                        <h3>Active Projects</h3>
                        <div id="activeProjectsList" class="projects-list">
                            <p class="no-projects">No active projects</p>
                        </div>
                    </div>
                </div>

                <!-- Main Canvas Area -->
                <div id="canvasContainer">
                    <canvas id="gameCanvas" width="800" height="600"></canvas>
                    <div id="canvasOverlay" class="canvas-overlay"></div>
                </div>

                <!-- Right Sidebar -->
                <div id="rightSidebar" class="sidebar">
                    <div class="sidebar-section">
                        <h3>Employees</h3>
                        <div id="employeesList" class="employees-list">
                            <p class="no-employees">No employees hired</p>
                        </div>
                    </div>
                    <div class="sidebar-section">
                        <h3>Market Trends</h3>
                        <div id="marketTrends" class="market-trends">
                            <div class="trend-item">
                                <span class="trend-name">Web Development</span>
                                <span class="trend-value">📈 High</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Status Bar -->
            <div id="bottomBar" class="ui-bar">
                <div id="notifications" class="notifications"></div>
                <div id="gameStatus" class="game-status">Ready</div>
            </div>
        </div>
    </div>

    <!-- Modal Container -->
    <div id="modalContainer" class="modal-container"></div>

    <!-- Scripts -->
    <script src="js/core/GameEngine.js"></script>
    <script src="js/core/EventSystem.js"></script>
    <script src="js/core/TimeManager.js"></script>
    <script src="js/core/SaveSystem.js"></script>
    <script src="js/systems/CompanySystem.js"></script>
    <script src="js/systems/EmployeeSystem.js"></script>
    <script src="js/systems/ProjectSystem.js"></script>
    <script src="js/systems/MarketSystem.js"></script>
    <script src="js/systems/ResearchSystem.js"></script>
    <script src="js/systems/OfficeSystem.js"></script>
    <script src="js/ui/UIManager.js"></script>
    <script src="js/ui/ModalManager.js"></script>
    <script src="js/ui/NotificationSystem.js"></script>
    <script src="js/rendering/Renderer.js"></script>
    <script src="js/rendering/OfficeRenderer.js"></script>
    <script src="js/data/GameData.js"></script>
    <script src="js/utils/Utils.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
