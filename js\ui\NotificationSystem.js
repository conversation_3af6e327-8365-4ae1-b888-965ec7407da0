/**
 * DevTycoon Pro - Notification System
 * Handles in-game notifications and alerts
 */

class NotificationSystem {
    constructor() {
        this.eventSystem = null;
        this.notifications = [];
        this.notificationContainer = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 seconds
        this.nextNotificationId = 1;
    }
    
    init(eventSystem) {
        this.eventSystem = eventSystem;
        this.notificationContainer = document.getElementById('notifications');
        
        if (!this.notificationContainer) {
            console.warn('Notification container not found, creating one...');
            this.createNotificationContainer();
        }
        
        console.log('🔔 Notification System initialized');
    }
    
    createNotificationContainer() {
        this.notificationContainer = document.createElement('div');
        this.notificationContainer.id = 'notifications';
        this.notificationContainer.className = 'notifications';
        
        // Try to add to bottom bar, or create a floating container
        const bottomBar = document.getElementById('bottomBar');
        if (bottomBar) {
            bottomBar.appendChild(this.notificationContainer);
        } else {
            this.notificationContainer.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 1000;
                max-width: 400px;
            `;
            document.body.appendChild(this.notificationContainer);
        }
    }
    
    show(notification) {
        const id = this.nextNotificationId++;
        const duration = notification.duration || this.defaultDuration;
        
        const notificationData = {
            id,
            type: notification.type || 'info',
            title: notification.title || '',
            message: notification.message || '',
            duration,
            timestamp: Date.now(),
            element: null
        };
        
        // Create notification element
        notificationData.element = this.createNotificationElement(notificationData);
        
        // Add to container
        this.notificationContainer.appendChild(notificationData.element);
        
        // Add to tracking array
        this.notifications.push(notificationData);
        
        // Animate in
        setTimeout(() => {
            notificationData.element.classList.add('animate-slide-in');
        }, 10);
        
        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }
        
        // Limit number of notifications
        this.enforceMaxNotifications();
        
        // Play notification sound
        this.playNotificationSound(notificationData.type);
        
        return id;
    }
    
    hide(notificationId) {
        const index = this.notifications.findIndex(n => n.id === notificationId);
        if (index === -1) return false;
        
        const notification = this.notifications[index];
        
        // Animate out
        notification.element.classList.add('animate-slide-out');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            this.notifications.splice(index, 1);
        }, 300);
        
        return true;
    }
    
    hideAll() {
        const notificationIds = this.notifications.map(n => n.id);
        notificationIds.forEach(id => this.hide(id));
    }
    
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type}`;
        element.setAttribute('data-notification-id', notification.id);
        
        // Add click to dismiss
        element.addEventListener('click', () => {
            this.hide(notification.id);
        });
        
        // Create content
        let content = '';
        
        if (notification.title) {
            content += `<div class="notification-title">${this.escapeHtml(notification.title)}</div>`;
        }
        
        if (notification.message) {
            content += `<div class="notification-message">${this.escapeHtml(notification.message)}</div>`;
        }
        
        // Add icon based on type
        const icon = this.getNotificationIcon(notification.type);
        if (icon) {
            content = `<span class="notification-icon">${icon}</span>` + content;
        }
        
        // Add close button
        content += '<button class="notification-close">&times;</button>';
        
        element.innerHTML = content;
        
        // Setup close button
        const closeBtn = element.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.hide(notification.id);
            });
        }
        
        return element;
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            achievement: '🏆',
            money: '💰',
            employee: '👥',
            project: '📁',
            research: '🔬'
        };
        
        return icons[type] || icons.info;
    }
    
    enforceMaxNotifications() {
        while (this.notifications.length > this.maxNotifications) {
            const oldestNotification = this.notifications[0];
            this.hide(oldestNotification.id);
        }
    }
    
    playNotificationSound(type) {
        // TODO: Implement audio system
        // For now, just log the sound that would be played
        console.log(`🔊 Playing ${type} notification sound`);
    }
    
    // Convenience methods for different notification types
    success(title, message, duration) {
        return this.show({
            type: 'success',
            title,
            message,
            duration
        });
    }
    
    error(title, message, duration) {
        return this.show({
            type: 'error',
            title,
            message,
            duration
        });
    }
    
    warning(title, message, duration) {
        return this.show({
            type: 'warning',
            title,
            message,
            duration
        });
    }
    
    info(title, message, duration) {
        return this.show({
            type: 'info',
            title,
            message,
            duration
        });
    }
    
    achievement(title, message, duration = 8000) {
        return this.show({
            type: 'achievement',
            title,
            message,
            duration
        });
    }
    
    money(amount, source, duration = 3000) {
        const message = source ? `from ${source}` : '';
        return this.show({
            type: 'money',
            title: `+$${this.formatNumber(amount)}`,
            message,
            duration
        });
    }
    
    // Special notification types
    showMoneyPopup(amount, x, y) {
        // Create floating money popup at specific coordinates
        const popup = document.createElement('div');
        popup.className = 'money-popup';
        popup.textContent = `+$${this.formatNumber(amount)}`;
        popup.style.left = `${x}px`;
        popup.style.top = `${y}px`;
        
        document.body.appendChild(popup);
        
        // Remove after animation
        setTimeout(() => {
            if (popup.parentNode) {
                popup.parentNode.removeChild(popup);
            }
        }, 2000);
    }
    
    showProgressNotification(title, progress, total) {
        const percentage = Math.round((progress / total) * 100);
        
        return this.show({
            type: 'info',
            title,
            message: `Progress: ${progress}/${total} (${percentage}%)`,
            duration: 0 // Don't auto-hide progress notifications
        });
    }
    
    updateProgressNotification(notificationId, progress, total) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return false;
        
        const percentage = Math.round((progress / total) * 100);
        const messageElement = notification.element.querySelector('.notification-message');
        
        if (messageElement) {
            messageElement.textContent = `Progress: ${progress}/${total} (${percentage}%)`;
        }
        
        // Auto-hide when complete
        if (progress >= total) {
            setTimeout(() => {
                this.hide(notificationId);
            }, 2000);
        }
        
        return true;
    }
    
    // Batch notifications for related events
    showBatch(notifications, delay = 500) {
        notifications.forEach((notification, index) => {
            setTimeout(() => {
                this.show(notification);
            }, index * delay);
        });
    }
    
    // Update method called by UI Manager
    update(deltaTime) {
        // Clean up expired notifications
        const now = Date.now();
        const expiredNotifications = this.notifications.filter(n => 
            n.duration > 0 && (now - n.timestamp) > n.duration
        );
        
        expiredNotifications.forEach(notification => {
            this.hide(notification.id);
        });
        
        // Update any animated elements
        this.updateAnimations();
    }
    
    updateAnimations() {
        // Update progress bars in notifications
        const progressBars = this.notificationContainer.querySelectorAll('.notification-progress');
        progressBars.forEach(bar => {
            // Add any progress bar animations here
        });
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    }
    
    // Settings
    setMaxNotifications(max) {
        this.maxNotifications = Math.max(1, max);
        this.enforceMaxNotifications();
    }
    
    setDefaultDuration(duration) {
        this.defaultDuration = Math.max(1000, duration);
    }
    
    // Debug methods
    getActiveNotifications() {
        return this.notifications.map(n => ({
            id: n.id,
            type: n.type,
            title: n.title,
            message: n.message,
            age: Date.now() - n.timestamp
        }));
    }
    
    showTestNotifications() {
        this.success('Success!', 'This is a success notification');
        setTimeout(() => this.error('Error!', 'This is an error notification'), 1000);
        setTimeout(() => this.warning('Warning!', 'This is a warning notification'), 2000);
        setTimeout(() => this.info('Info', 'This is an info notification'), 3000);
        setTimeout(() => this.achievement('Achievement Unlocked!', 'You unlocked a test achievement'), 4000);
    }
}
