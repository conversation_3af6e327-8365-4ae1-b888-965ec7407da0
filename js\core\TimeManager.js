/**
 * DevTycoon Pro - Time Manager
 * Manages game time, date progression, and time-based events
 */

class TimeManager {
    constructor() {
        // Game time settings
        this.gameStartYear = 2000;
        this.currentDate = new Date(this.gameStartYear, 0, 1); // January 1st
        this.gameSpeed = 1;
        this.isPaused = false;
        
        // Time progression
        this.realTimePerGameDay = 60; // 60 seconds = 1 game day
        this.gameTimeAccumulator = 0;
        this.totalGameTime = 0;
        
        // Time events
        this.scheduledEvents = [];
        this.recurringEvents = [];
        this.timeEventId = 0;
        
        // Performance tracking
        this.lastUpdate = 0;
        this.updateFrequency = 1000; // Update UI every second
        
        // Game calendar
        this.workingDays = [1, 2, 3, 4, 5]; // Monday to Friday
        this.workingHours = { start: 9, end: 17 }; // 9 AM to 5 PM
        this.holidays = [];
        
        // Time multipliers for different activities
        this.timeMultipliers = {
            development: 1.0,
            research: 1.2,
            marketing: 0.8,
            training: 1.5
        };
        
        this.eventSystem = null;
    }
    
    init(gameEngine) {
        this.eventSystem = gameEngine.eventSystem;
        this.setupTimeEvents();
        console.log('⏰ Time Manager initialized');
    }
    
    setupTimeEvents() {
        // Listen for game speed changes
        this.eventSystem.on('gameSpeedChanged', (speed) => {
            this.gameSpeed = speed;
        });
        
        // Listen for pause/resume
        this.eventSystem.on('gamePaused', () => {
            this.isPaused = true;
        });
        
        this.eventSystem.on('gameResumed', () => {
            this.isPaused = false;
        });
    }
    
    update(deltaTime) {
        if (this.isPaused) return;
        
        // Accumulate game time
        this.gameTimeAccumulator += deltaTime * this.gameSpeed;
        this.totalGameTime += deltaTime * this.gameSpeed;
        
        // Check if a game day has passed
        const gameSecondsPerDay = this.realTimePerGameDay;
        if (this.gameTimeAccumulator >= gameSecondsPerDay) {
            const daysToAdvance = Math.floor(this.gameTimeAccumulator / gameSecondsPerDay);
            this.gameTimeAccumulator -= daysToAdvance * gameSecondsPerDay;
            
            this.advanceDays(daysToAdvance);
        }
        
        // Process scheduled events
        this.processScheduledEvents();
        
        // Update UI periodically
        const now = Date.now();
        if (now - this.lastUpdate >= this.updateFrequency) {
            this.updateUI();
            this.lastUpdate = now;
        }
    }
    
    advanceDays(days) {
        for (let i = 0; i < days; i++) {
            this.currentDate.setDate(this.currentDate.getDate() + 1);
            this.onDayAdvanced();
        }
        
        this.eventSystem.emit('timeAdvanced', {
            currentDate: new Date(this.currentDate),
            daysAdvanced: days
        });
    }
    
    onDayAdvanced() {
        const dayOfWeek = this.currentDate.getDay();
        const isWorkingDay = this.workingDays.includes(dayOfWeek);
        const isHoliday = this.isHoliday(this.currentDate);
        
        // Emit day-specific events
        this.eventSystem.emit('newDay', {
            date: new Date(this.currentDate),
            isWorkingDay,
            isHoliday,
            dayOfWeek
        });
        
        if (isWorkingDay && !isHoliday) {
            this.eventSystem.emit('workDay', {
                date: new Date(this.currentDate)
            });
        } else {
            this.eventSystem.emit('nonWorkDay', {
                date: new Date(this.currentDate),
                isHoliday
            });
        }
        
        // Check for month/year changes
        if (this.currentDate.getDate() === 1) {
            this.onMonthAdvanced();
        }
    }
    
    onMonthAdvanced() {
        this.eventSystem.emit('newMonth', {
            date: new Date(this.currentDate),
            month: this.currentDate.getMonth(),
            year: this.currentDate.getFullYear()
        });
        
        // Check for year change
        if (this.currentDate.getMonth() === 0) {
            this.onYearAdvanced();
        }
    }
    
    onYearAdvanced() {
        this.eventSystem.emit('newYear', {
            date: new Date(this.currentDate),
            year: this.currentDate.getFullYear()
        });
    }
    
    scheduleEvent(callback, delay, data = null) {
        const eventId = this.timeEventId++;
        const executeTime = this.totalGameTime + delay;
        
        this.scheduledEvents.push({
            id: eventId,
            callback,
            executeTime,
            data
        });
        
        // Sort by execution time
        this.scheduledEvents.sort((a, b) => a.executeTime - b.executeTime);
        
        return eventId;
    }
    
    scheduleRecurringEvent(callback, interval, data = null, startDelay = 0) {
        const eventId = this.timeEventId++;
        const nextExecuteTime = this.totalGameTime + startDelay;
        
        this.recurringEvents.push({
            id: eventId,
            callback,
            interval,
            nextExecuteTime,
            data
        });
        
        return eventId;
    }
    
    cancelEvent(eventId) {
        // Remove from scheduled events
        this.scheduledEvents = this.scheduledEvents.filter(event => event.id !== eventId);
        
        // Remove from recurring events
        this.recurringEvents = this.recurringEvents.filter(event => event.id !== eventId);
    }
    
    processScheduledEvents() {
        // Process one-time events
        while (this.scheduledEvents.length > 0 && 
               this.scheduledEvents[0].executeTime <= this.totalGameTime) {
            const event = this.scheduledEvents.shift();
            try {
                event.callback(event.data);
            } catch (error) {
                console.error('Error executing scheduled event:', error);
            }
        }
        
        // Process recurring events
        for (const event of this.recurringEvents) {
            if (event.nextExecuteTime <= this.totalGameTime) {
                try {
                    event.callback(event.data);
                    event.nextExecuteTime += event.interval;
                } catch (error) {
                    console.error('Error executing recurring event:', error);
                }
            }
        }
    }
    
    isWorkingDay(date = this.currentDate) {
        const dayOfWeek = date.getDay();
        return this.workingDays.includes(dayOfWeek) && !this.isHoliday(date);
    }
    
    isWorkingHour(hour = this.currentDate.getHours()) {
        return hour >= this.workingHours.start && hour < this.workingHours.end;
    }
    
    isHoliday(date = this.currentDate) {
        return this.holidays.some(holiday => 
            holiday.getMonth() === date.getMonth() && 
            holiday.getDate() === date.getDate()
        );
    }
    
    addHoliday(month, day, name) {
        this.holidays.push({
            month,
            day,
            name,
            getMonth: () => month,
            getDate: () => day
        });
    }
    
    getTimeMultiplier(activity) {
        return this.timeMultipliers[activity] || 1.0;
    }
    
    setTimeMultiplier(activity, multiplier) {
        this.timeMultipliers[activity] = multiplier;
    }
    
    formatDate(date = this.currentDate, format = 'full') {
        const options = {
            full: { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            },
            short: { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            },
            compact: { 
                year: '2-digit', 
                month: '2-digit', 
                day: '2-digit' 
            }
        };
        
        return date.toLocaleDateString('en-US', options[format] || options.full);
    }
    
    getGameAge() {
        const startDate = new Date(this.gameStartYear, 0, 1);
        const diffTime = this.currentDate - startDate;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        const years = Math.floor(diffDays / 365);
        const months = Math.floor((diffDays % 365) / 30);
        const days = diffDays % 30;
        
        return { years, months, days, totalDays: diffDays };
    }
    
    getTimeUntilNextWorkDay() {
        const nextDate = new Date(this.currentDate);
        let daysToAdd = 1;
        
        while (!this.isWorkingDay(nextDate)) {
            nextDate.setDate(nextDate.getDate() + 1);
            daysToAdd++;
        }
        
        return daysToAdd - 1;
    }
    
    updateUI() {
        // Update date display
        const dateDisplay = document.getElementById('dateDisplay');
        if (dateDisplay) {
            dateDisplay.textContent = this.formatDate(this.currentDate, 'short');
        }
        
        // Update game age in company info if available
        const gameAge = this.getGameAge();
        this.eventSystem.emit('uiUpdate', {
            type: 'date',
            date: this.currentDate,
            formattedDate: this.formatDate(this.currentDate, 'short'),
            gameAge,
            isWorkingDay: this.isWorkingDay(),
            isWorkingHour: this.isWorkingHour()
        });
    }
    
    // Save/Load functionality
    getSaveData() {
        return {
            gameStartYear: this.gameStartYear,
            currentDate: this.currentDate.toISOString(),
            totalGameTime: this.totalGameTime,
            gameTimeAccumulator: this.gameTimeAccumulator,
            holidays: this.holidays,
            timeMultipliers: this.timeMultipliers
        };
    }
    
    loadSaveData(data) {
        this.gameStartYear = data.gameStartYear || 2000;
        this.currentDate = new Date(data.currentDate || new Date(this.gameStartYear, 0, 1));
        this.totalGameTime = data.totalGameTime || 0;
        this.gameTimeAccumulator = data.gameTimeAccumulator || 0;
        this.holidays = data.holidays || [];
        this.timeMultipliers = data.timeMultipliers || this.timeMultipliers;
        
        this.updateUI();
    }
    
    // Debug methods
    skipToDate(year, month, day) {
        this.currentDate = new Date(year, month - 1, day);
        this.updateUI();
        this.eventSystem.emit('timeSkipped', {
            newDate: new Date(this.currentDate)
        });
    }
    
    addDays(days) {
        this.advanceDays(days);
    }
}
