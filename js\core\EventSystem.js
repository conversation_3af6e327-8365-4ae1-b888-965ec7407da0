/**
 * DevTycoon Pro - Event System
 * Handles game-wide event communication between systems
 */

class EventSystem {
    constructor() {
        this.listeners = new Map();
        this.eventQueue = [];
        this.isProcessing = false;
        this.maxEventsPerFrame = 50;
        this.eventHistory = [];
        this.maxHistorySize = 1000;
        
        // Performance tracking
        this.eventStats = {
            totalEvents: 0,
            eventsThisFrame: 0,
            averageProcessingTime: 0
        };
    }
    
    /**
     * Subscribe to an event
     * @param {string} eventName - Name of the event
     * @param {function} callback - Function to call when event is fired
     * @param {object} options - Additional options (once, priority)
     * @returns {function} Unsubscribe function
     */
    on(eventName, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('Event callback must be a function');
        }
        
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        
        const listener = {
            callback,
            once: options.once || false,
            priority: options.priority || 0,
            id: this.generateListenerId()
        };
        
        const listeners = this.listeners.get(eventName);
        listeners.push(listener);
        
        // Sort by priority (higher priority first)
        listeners.sort((a, b) => b.priority - a.priority);
        
        // Return unsubscribe function
        return () => this.off(eventName, listener.id);
    }
    
    /**
     * Subscribe to an event that fires only once
     * @param {string} eventName - Name of the event
     * @param {function} callback - Function to call when event is fired
     * @param {number} priority - Event priority
     * @returns {function} Unsubscribe function
     */
    once(eventName, callback, priority = 0) {
        return this.on(eventName, callback, { once: true, priority });
    }
    
    /**
     * Unsubscribe from an event
     * @param {string} eventName - Name of the event
     * @param {string|function} callbackOrId - Callback function or listener ID
     */
    off(eventName, callbackOrId) {
        if (!this.listeners.has(eventName)) return;
        
        const listeners = this.listeners.get(eventName);
        const index = listeners.findIndex(listener => 
            listener.id === callbackOrId || listener.callback === callbackOrId
        );
        
        if (index !== -1) {
            listeners.splice(index, 1);
            
            // Clean up empty listener arrays
            if (listeners.length === 0) {
                this.listeners.delete(eventName);
            }
        }
    }
    
    /**
     * Remove all listeners for an event
     * @param {string} eventName - Name of the event
     */
    removeAllListeners(eventName) {
        if (eventName) {
            this.listeners.delete(eventName);
        } else {
            this.listeners.clear();
        }
    }
    
    /**
     * Emit an event immediately
     * @param {string} eventName - Name of the event
     * @param {*} data - Data to pass to listeners
     * @param {object} options - Additional options
     */
    emit(eventName, data = null, options = {}) {
        const event = {
            name: eventName,
            data,
            timestamp: Date.now(),
            immediate: options.immediate || false,
            source: options.source || 'unknown'
        };
        
        if (event.immediate || !this.isProcessing) {
            this.processEvent(event);
        } else {
            this.eventQueue.push(event);
        }
        
        this.addToHistory(event);
    }
    
    /**
     * Queue an event to be processed later
     * @param {string} eventName - Name of the event
     * @param {*} data - Data to pass to listeners
     * @param {object} options - Additional options
     */
    queue(eventName, data = null, options = {}) {
        const event = {
            name: eventName,
            data,
            timestamp: Date.now(),
            immediate: false,
            source: options.source || 'unknown'
        };
        
        this.eventQueue.push(event);
        this.addToHistory(event);
    }
    
    /**
     * Process queued events
     * Called by the game engine each frame
     */
    processQueue() {
        if (this.isProcessing || this.eventQueue.length === 0) return;
        
        this.isProcessing = true;
        this.eventStats.eventsThisFrame = 0;
        
        const startTime = performance.now();
        const eventsToProcess = Math.min(this.eventQueue.length, this.maxEventsPerFrame);
        
        for (let i = 0; i < eventsToProcess; i++) {
            const event = this.eventQueue.shift();
            this.processEvent(event);
            this.eventStats.eventsThisFrame++;
        }
        
        const processingTime = performance.now() - startTime;
        this.updateProcessingStats(processingTime);
        
        this.isProcessing = false;
    }
    
    /**
     * Process a single event
     * @param {object} event - Event to process
     */
    processEvent(event) {
        if (!this.listeners.has(event.name)) return;
        
        const listeners = this.listeners.get(event.name);
        const listenersToRemove = [];
        
        for (const listener of listeners) {
            try {
                listener.callback(event.data, event);
                
                if (listener.once) {
                    listenersToRemove.push(listener.id);
                }
            } catch (error) {
                console.error(`Error in event listener for '${event.name}':`, error);
                this.emit('eventError', {
                    eventName: event.name,
                    error,
                    listener
                }, { immediate: true });
            }
        }
        
        // Remove one-time listeners
        for (const listenerId of listenersToRemove) {
            this.off(event.name, listenerId);
        }
        
        this.eventStats.totalEvents++;
    }
    
    /**
     * Check if there are listeners for an event
     * @param {string} eventName - Name of the event
     * @returns {boolean} True if there are listeners
     */
    hasListeners(eventName) {
        return this.listeners.has(eventName) && this.listeners.get(eventName).length > 0;
    }
    
    /**
     * Get the number of listeners for an event
     * @param {string} eventName - Name of the event
     * @returns {number} Number of listeners
     */
    getListenerCount(eventName) {
        return this.listeners.has(eventName) ? this.listeners.get(eventName).length : 0;
    }
    
    /**
     * Get all event names that have listeners
     * @returns {string[]} Array of event names
     */
    getEventNames() {
        return Array.from(this.listeners.keys());
    }
    
    /**
     * Clear the event queue
     */
    clearQueue() {
        this.eventQueue.length = 0;
    }
    
    /**
     * Get event statistics
     * @returns {object} Event statistics
     */
    getStats() {
        return {
            ...this.eventStats,
            queueSize: this.eventQueue.length,
            listenerCount: Array.from(this.listeners.values()).reduce((sum, listeners) => sum + listeners.length, 0),
            eventTypes: this.listeners.size
        };
    }
    
    /**
     * Get recent event history
     * @param {number} count - Number of recent events to return
     * @returns {object[]} Array of recent events
     */
    getHistory(count = 10) {
        return this.eventHistory.slice(-count);
    }
    
    /**
     * Generate a unique listener ID
     * @returns {string} Unique ID
     */
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Add event to history
     * @param {object} event - Event to add to history
     */
    addToHistory(event) {
        this.eventHistory.push({
            ...event,
            processed: false
        });
        
        // Limit history size
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.splice(0, this.eventHistory.length - this.maxHistorySize);
        }
    }
    
    /**
     * Update processing statistics
     * @param {number} processingTime - Time taken to process events
     */
    updateProcessingStats(processingTime) {
        // Calculate moving average of processing time
        const alpha = 0.1; // Smoothing factor
        this.eventStats.averageProcessingTime = 
            this.eventStats.averageProcessingTime * (1 - alpha) + processingTime * alpha;
    }
    
    /**
     * Create a namespaced event emitter
     * @param {string} namespace - Namespace for events
     * @returns {object} Namespaced event emitter
     */
    createNamespace(namespace) {
        return {
            on: (eventName, callback, options) => 
                this.on(`${namespace}:${eventName}`, callback, options),
            once: (eventName, callback, priority) => 
                this.once(`${namespace}:${eventName}`, callback, priority),
            off: (eventName, callbackOrId) => 
                this.off(`${namespace}:${eventName}`, callbackOrId),
            emit: (eventName, data, options) => 
                this.emit(`${namespace}:${eventName}`, data, { ...options, source: namespace }),
            queue: (eventName, data, options) => 
                this.queue(`${namespace}:${eventName}`, data, { ...options, source: namespace })
        };
    }
    
    /**
     * Debug method to log all listeners
     */
    debugListeners() {
        console.group('Event System Debug');
        for (const [eventName, listeners] of this.listeners) {
            console.log(`${eventName}: ${listeners.length} listeners`);
            listeners.forEach((listener, index) => {
                console.log(`  ${index + 1}. Priority: ${listener.priority}, Once: ${listener.once}`);
            });
        }
        console.groupEnd();
    }
}
