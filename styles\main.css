/* DevTycoon Pro - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

#gameContainer {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
}

.screen.active {
    display: flex;
}

/* Loading Screen */
#loadingScreen {
    background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #ff00ff);
    width: 0%;
    transition: width 0.3s ease;
}

/* Main Menu */
#mainMenu {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.menu-content {
    text-align: center;
    max-width: 500px;
}

.game-title {
    font-size: 4rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.menu-btn.primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.menu-btn.primary:hover {
    background: linear-gradient(45deg, #ff5252, #e55100);
}

/* Company Setup */
#companySetup {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
}

.setup-content {
    background: rgba(255, 255, 255, 0.1);
    padding: 3rem;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    max-width: 600px;
    width: 90%;
}

.setup-content h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Game Screen */
#gameScreen {
    flex-direction: column;
    background: #1a1a1a;
}

/* UI Bars */
.ui-bar {
    background: linear-gradient(90deg, #2c3e50, #34495e);
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid #3498db;
    z-index: 100;
}

#topBar {
    border-bottom: 2px solid #3498db;
}

#bottomBar {
    border-top: 2px solid #3498db;
    border-bottom: none;
}

.company-info,
.game-stats,
.game-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.company-info span,
.game-stats span {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 600;
}

.control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Game Area */
#gameArea {
    flex: 1;
    display: flex;
    height: calc(100vh - 120px);
}

/* Sidebars */
.sidebar {
    width: 250px;
    background: #2c3e50;
    border-right: 2px solid #34495e;
    overflow-y: auto;
    padding: 1rem;
}

#rightSidebar {
    border-right: none;
    border-left: 2px solid #34495e;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    color: #3498db;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #34495e;
}

.action-btn {
    width: 100%;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid #3498db;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.action-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: translateX(4px);
}

/* Canvas Container */
#canvasContainer {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
}

#gameCanvas {
    border: 2px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
}

.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Lists */
.projects-list,
.employees-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-projects,
.no-employees {
    color: #7f8c8d;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

/* Market Trends */
.trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.trend-value {
    font-weight: 600;
}

/* Notifications */
.notifications {
    flex: 1;
}

.game-status {
    color: #27ae60;
    font-weight: 600;
}

/* Modal Container */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-container.active {
    display: flex;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sidebar {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .sidebar {
        width: 180px;
    }
    
    .company-info,
    .game-stats {
        gap: 0.8rem;
    }
    
    .company-info span,
    .game-stats span {
        padding: 0.3rem 0.6rem;
        font-size: 0.9rem;
    }
}
