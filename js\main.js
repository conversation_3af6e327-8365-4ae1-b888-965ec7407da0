/**
 * DevTycoon Pro - Main Entry Point
 * Initializes the game and handles the loading sequence
 */

// Global game state
let gameEngine = null;
let loadingProgress = 0;
let loadingSteps = [
    'Initializing core systems...',
    'Loading game data...',
    'Setting up event handlers...',
    'Initializing graphics...',
    'Loading audio assets...',
    'Preparing user interface...',
    'Finalizing setup...'
];

// DOM elements
let loadingScreen = null;
let loadingProgressBar = null;
let loadingText = null;

/**
 * Initialize the game when the page loads
 */
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🎮 DevTycoon Pro - Starting initialization...');
    
    // Get loading screen elements
    loadingScreen = document.getElementById('loadingScreen');
    loadingProgressBar = document.getElementById('loadingProgress');
    loadingText = document.getElementById('loadingText');
    
    try {
        // Start loading sequence
        await startLoadingSequence();
        
        // Initialize game engine
        gameEngine = new GameEngine();
        window.gameEngine = gameEngine;
        
        // Setup main menu handlers
        setupMainMenuHandlers();
        
        // Setup company setup handlers
        setupCompanySetupHandlers();
        
        // Setup game screen handlers
        setupGameScreenHandlers();
        
        // Complete loading
        await completeLoading();
        
        console.log('✅ Game initialization complete!');
        
    } catch (error) {
        console.error('❌ Failed to initialize game:', error);
        showLoadingError(error);
    }
});

/**
 * Simulate loading sequence with progress updates
 */
async function startLoadingSequence() {
    for (let i = 0; i < loadingSteps.length; i++) {
        updateLoadingProgress(i, loadingSteps[i]);
        await sleep(300 + Math.random() * 500); // Simulate loading time
    }
    
    updateLoadingProgress(loadingSteps.length, 'Ready to play!');
    await sleep(500);
}

/**
 * Update loading progress
 */
function updateLoadingProgress(step, text) {
    loadingProgress = (step / loadingSteps.length) * 100;
    
    if (loadingProgressBar) {
        loadingProgressBar.style.width = `${loadingProgress}%`;
    }
    
    if (loadingText) {
        loadingText.textContent = text;
    }
}

/**
 * Complete the loading sequence and show main menu
 */
async function completeLoading() {
    await sleep(500);
    
    // Hide loading screen and show main menu
    if (loadingScreen) {
        loadingScreen.classList.add('animate-fade-out');
        setTimeout(() => {
            loadingScreen.classList.remove('active');
            showMainMenu();
        }, 300);
    }
}

/**
 * Show main menu
 */
function showMainMenu() {
    const mainMenu = document.getElementById('mainMenu');
    if (mainMenu) {
        mainMenu.classList.add('active', 'animate-fade-in');
    }
    
    if (gameEngine) {
        gameEngine.eventSystem.emit('gameStateChange', 'menu');
    }
}

/**
 * Setup main menu event handlers
 */
function setupMainMenuHandlers() {
    // New Game button
    const newGameBtn = document.getElementById('newGameBtn');
    if (newGameBtn) {
        newGameBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            showCompanySetup();
        });
    }

    // Load Game button
    const loadGameBtn = document.getElementById('loadGameBtn');
    if (loadGameBtn) {
        loadGameBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            loadGame();
        });
    }

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            showSettings();
        });
    }

    // Credits button
    const creditsBtn = document.getElementById('creditsBtn');
    if (creditsBtn) {
        creditsBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            showCredits();
        });
    }
}

/**
 * Setup company setup screen handlers
 */
function setupCompanySetupHandlers() {
    // Start Game button
    const startGameBtn = document.getElementById('startGameBtn');
    if (startGameBtn) {
        startGameBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            startNewGame();
        });
    }

    // Back to Menu button
    const backToMenuBtn = document.getElementById('backToMenuBtn');
    if (backToMenuBtn) {
        backToMenuBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            showMainMenu();
        });
    }
}

/**
 * Setup game screen handlers
 */
function setupGameScreenHandlers() {
    // Pause button
    const pauseBtn = document.getElementById('pauseBtn');
    if (pauseBtn) {
        pauseBtn.addEventListener('click', () => {
            if (gameEngine) {
                gameEngine.isPaused ? gameEngine.resume() : gameEngine.pause();
                updatePauseButton();
            }
        });
    }
    
    // Speed button
    const speedBtn = document.getElementById('speedBtn');
    if (speedBtn) {
        speedBtn.addEventListener('click', () => {
            if (gameEngine) {
                cycleGameSpeed();
            }
        });
    }
    
    // Menu button
    const menuBtn = document.getElementById('menuBtn');
    if (menuBtn) {
        menuBtn.addEventListener('click', (e) => {
            playButtonSound(e.target);
            showGameMenu();
        });
    }
    
    // Action buttons
    setupActionButtons();
}

/**
 * Setup action buttons in the sidebar
 */
function setupActionButtons() {
    const actionButtons = [
        { id: 'hireEmployeeBtn', action: 'hireEmployee' },
        { id: 'newProjectBtn', action: 'newProject' },
        { id: 'researchBtn', action: 'research' },
        { id: 'marketingBtn', action: 'marketing' },
        { id: 'officeBtn', action: 'office' }
    ];
    
    actionButtons.forEach(({ id, action }) => {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', (e) => {
                playButtonSound(e.target);
                handleActionButton(action);
            });
        }
    });
}

/**
 * Show company setup screen
 */
function showCompanySetup() {
    hideAllScreens();
    const companySetup = document.getElementById('companySetup');
    if (companySetup) {
        companySetup.classList.add('active', 'animate-fade-in');
    }
    
    if (gameEngine) {
        gameEngine.eventSystem.emit('gameStateChange', 'setup');
    }
}

/**
 * Start a new game with company setup
 */
function startNewGame() {
    const companyName = document.getElementById('companyName')?.value || 'My Company';
    const founderName = document.getElementById('founderName')?.value || 'Player';
    const difficulty = document.getElementById('difficulty')?.value || 'normal';
    const startYear = parseInt(document.getElementById('startYear')?.value) || 2000;
    
    // Validate inputs
    if (companyName.trim().length === 0) {
        showNotification('Please enter a company name', 'error');
        return;
    }
    
    if (founderName.trim().length === 0) {
        showNotification('Please enter your name', 'error');
        return;
    }
    
    // Create company data
    const companyData = {
        name: companyName.trim(),
        founder: founderName.trim(),
        difficulty,
        startYear,
        startingCapital: getStartingCapital(difficulty)
    };
    
    // Initialize game with company data
    if (gameEngine) {
        gameEngine.getSystem('company').createCompany(companyData);
        gameEngine.timeManager.gameStartYear = startYear;
        gameEngine.timeManager.currentDate = new Date(startYear, 0, 1);
        
        showGameScreen();
        gameEngine.eventSystem.emit('gameStateChange', 'playing');
        
        showNotification(`Welcome to ${companyName}!`, 'success');
    }
}

/**
 * Get starting capital based on difficulty
 */
function getStartingCapital(difficulty) {
    const capitals = {
        easy: 50000,
        normal: 25000,
        hard: 10000,
        expert: 5000
    };
    return capitals[difficulty] || 25000;
}

/**
 * Show game screen
 */
function showGameScreen() {
    hideAllScreens();
    const gameScreen = document.getElementById('gameScreen');
    if (gameScreen) {
        gameScreen.classList.add('active', 'animate-fade-in');
    }
}

/**
 * Hide all screens
 */
function hideAllScreens() {
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => {
        screen.classList.remove('active', 'animate-fade-in', 'animate-fade-out');
    });
}

/**
 * Handle action button clicks
 */
function handleActionButton(action) {
    if (!gameEngine) return;
    
    switch (action) {
        case 'hireEmployee':
            gameEngine.getSystem('ui').showModal('hireEmployee');
            break;
        case 'newProject':
            gameEngine.getSystem('ui').showModal('newProject');
            break;
        case 'research':
            gameEngine.getSystem('ui').showModal('research');
            break;
        case 'marketing':
            gameEngine.getSystem('ui').showModal('marketing');
            break;
        case 'office':
            gameEngine.getSystem('ui').showModal('office');
            break;
    }
}

/**
 * Cycle through game speeds
 */
function cycleGameSpeed() {
    const speeds = [0.5, 1, 2, 3, 5];
    const currentSpeed = gameEngine.gameSpeed;
    const currentIndex = speeds.indexOf(currentSpeed);
    const nextIndex = (currentIndex + 1) % speeds.length;
    const newSpeed = speeds[nextIndex];
    
    gameEngine.setGameSpeed(newSpeed);
    updateSpeedButton(newSpeed);
}

/**
 * Update pause button appearance
 */
function updatePauseButton() {
    const pauseBtn = document.getElementById('pauseBtn');
    if (pauseBtn && gameEngine) {
        pauseBtn.textContent = gameEngine.isPaused ? '▶️' : '⏸️';
        pauseBtn.title = gameEngine.isPaused ? 'Resume' : 'Pause';
    }
}

/**
 * Update speed button appearance
 */
function updateSpeedButton(speed) {
    const speedBtn = document.getElementById('speedBtn');
    if (speedBtn) {
        speedBtn.textContent = `${speed}x`;
        speedBtn.setAttribute('data-speed', speed);
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    if (gameEngine && gameEngine.eventSystem) {
        gameEngine.eventSystem.emit('notification', {
            type,
            title: type.charAt(0).toUpperCase() + type.slice(1),
            message
        });
    } else {
        // Fallback for early notifications
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * Play button sound effect
 */
function playButtonSound(targetElement) {
    // TODO: Implement audio system
    // For now, just add visual feedback
    if (targetElement) {
        targetElement.classList.add('btn-press');
        setTimeout(() => {
            targetElement.classList.remove('btn-press');
        }, 100);
    }
}

/**
 * Load existing game
 */
function loadGame() {
    // TODO: Implement load game functionality
    showNotification('Load game functionality coming soon!', 'info');
}

/**
 * Show settings
 */
function showSettings() {
    // TODO: Implement settings modal
    showNotification('Settings coming soon!', 'info');
}

/**
 * Show credits
 */
function showCredits() {
    // TODO: Implement credits modal
    showNotification('Credits: DevTycoon Pro - Built with passion for game development!', 'info');
}

/**
 * Show game menu
 */
function showGameMenu() {
    // TODO: Implement in-game menu
    showNotification('Game menu coming soon!', 'info');
}

/**
 * Show loading error
 */
function showLoadingError(error) {
    if (loadingText) {
        loadingText.textContent = `Error: ${error.message}`;
        loadingText.style.color = '#e74c3c';
    }
}

/**
 * Utility function for delays
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Global error handler
window.addEventListener('error', (event) => {
    if (event.error) {
        console.error('Global error:', event.error);
        if (gameEngine && gameEngine.eventSystem) {
            gameEngine.eventSystem.emit('notification', {
                type: 'error',
                title: 'Error',
                message: 'An unexpected error occurred.'
            });
        }
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    if (event.reason) {
        console.error('Unhandled promise rejection:', event.reason);
        if (gameEngine && gameEngine.eventSystem) {
            gameEngine.eventSystem.emit('notification', {
                type: 'error',
                title: 'Error',
                message: 'An unexpected error occurred.'
            });
        }
    }
});
